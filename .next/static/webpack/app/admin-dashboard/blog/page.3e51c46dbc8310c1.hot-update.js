"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/blog/page",{

/***/ "(app-pages-browser)/./src/components/admin/blog/blogs-management.tsx":
/*!********************************************************!*\
  !*** ./src/components/admin/blog/blogs-management.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlogsManagement: () => (/* binding */ BlogsManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EllipsisVerticalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PowerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentDuplicateIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArchiveBoxIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js\");\n/* harmony import */ var _blog_modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./blog-modal */ \"(app-pages-browser)/./src/components/admin/blog/blog-modal.tsx\");\n/* harmony import */ var _components_providers_notification_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/notification-provider */ \"(app-pages-browser)/./src/components/providers/notification-provider.tsx\");\n/* harmony import */ var _shared_confirmation_modal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/confirmation-modal */ \"(app-pages-browser)/./src/components/admin/shared/confirmation-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ BlogsManagement auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nfunction BlogAvatar(param) {\n    let { title, featuredImageUrl, size = 'md', className = '', style = {} } = param;\n    _s();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [imageLoading, setImageLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // Size configurations\n    const sizeClasses = {\n        xs: 'w-6 h-6',\n        sm: 'w-8 h-8',\n        md: 'w-12 h-12',\n        lg: 'w-16 h-16',\n        xl: 'w-24 h-24',\n        'full-height': 'w-full h-full'\n    };\n    const textSizes = {\n        xs: 'text-xs',\n        sm: 'text-sm',\n        md: 'text-base',\n        lg: 'text-lg',\n        xl: 'text-xl',\n        'full-height': 'text-4xl'\n    };\n    // Generate initials from blog title\n    const getInitials = (title)=>{\n        return title.split(' ').map((word)=>word.charAt(0)).join('').toUpperCase().slice(0, 2);\n    };\n    // Generate a consistent color based on the blog title\n    const getBackgroundColor = (title)=>{\n        const colors = [\n            'bg-blue-500',\n            'bg-green-500',\n            'bg-purple-500',\n            'bg-pink-500',\n            'bg-indigo-500',\n            'bg-yellow-500',\n            'bg-red-500',\n            'bg-teal-500',\n            'bg-orange-500',\n            'bg-cyan-500'\n        ];\n        let hash = 0;\n        for(let i = 0; i < title.length; i++){\n            hash = title.charCodeAt(i) + ((hash << 5) - hash);\n        }\n        return colors[Math.abs(hash) % colors.length];\n    };\n    const handleImageLoad = ()=>{\n        setImageLoading(false);\n        setImageError(false);\n    };\n    const handleImageError = ()=>{\n        setImageLoading(false);\n        setImageError(true);\n    };\n    const baseClasses = \"\\n    \".concat(sizeClasses[size], \" \\n    rounded-lg \\n    flex \\n    items-center \\n    justify-center \\n    overflow-hidden \\n    \").concat(size === 'full-height' ? 'min-h-[320px]' : '', \"\\n    \").concat(className, \"\\n  \");\n    // If we have a valid featured image URL and no error, show the image\n    if (featuredImageUrl && !imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(baseClasses, \" bg-gray-100 relative\"),\n            style: style,\n            children: [\n                imageLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 w-6 h-6\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: featuredImageUrl,\n                    alt: \"\".concat(title, \" featured image\"),\n                    className: \"\\n            w-full h-full object-cover\\n            \".concat(imageLoading ? 'opacity-0' : 'opacity-100', \"\\n            transition-opacity duration-200\\n          \"),\n                    onLoad: handleImageLoad,\n                    onError: handleImageError\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 271,\n            columnNumber: 7\n        }, this);\n    }\n    // Fallback: Show initials with colored background\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\\n        \".concat(baseClasses, \" \\n        \").concat(getBackgroundColor(title), \" \\n        text-white \\n        font-semibold \\n        \").concat(textSizes[size], \"\\n        shadow-sm\\n      \"),\n        style: style,\n        title: title,\n        children: size === 'full-height' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-24 h-24 text-white opacity-80\"\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: getInitials(title)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm opacity-90 break-words line-clamp-3\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 310,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            children: getInitials(title)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 318,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n        lineNumber: 297,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogAvatar, \"yA6MC4/13YXgE42AlKw5vrWMK58=\");\n_c = BlogAvatar;\nfunction BlogsManagement(param) {\n    let { config } = param;\n    var _config_defaultSort, _config_defaultSort1, _config_defaultViewSettings, _config_defaultViewSettings1, _config_defaultViewSettings2, _config_defaultViewSettings3, _config_defaultViewSettings4, _config_defaultViewSettings5, _config_filters, _config_filters1, _config_permissions, _deleteConfirmation_bulkPosts, _deleteConfirmation_bulkPosts1, _deleteConfirmation_post;\n    _s1();\n    // Notification system\n    const { showSuccess, showError, showInfo, showLoading } = (0,_components_providers_notification_provider__WEBPACK_IMPORTED_MODULE_4__.useNotifications)();\n    // State management\n    const [blogPosts, setBlogPosts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [debouncedSearchQuery, setDebouncedSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultSort = config.defaultSort) === null || _config_defaultSort === void 0 ? void 0 : _config_defaultSort.field) || 'updatedAt');\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultSort1 = config.defaultSort) === null || _config_defaultSort1 === void 0 ? void 0 : _config_defaultSort1.direction) === 'asc' ? 'asc' : 'desc');\n    const [selectedPosts, setSelectedPosts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultViewSettings = config.defaultViewSettings) === null || _config_defaultViewSettings === void 0 ? void 0 : _config_defaultViewSettings.mode) === 'list' || ((_config_defaultViewSettings1 = config.defaultViewSettings) === null || _config_defaultViewSettings1 === void 0 ? void 0 : _config_defaultViewSettings1.mode) === 'grid' ? config.defaultViewSettings.mode : 'list');\n    const [density, setDensity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultViewSettings2 = config.defaultViewSettings) === null || _config_defaultViewSettings2 === void 0 ? void 0 : _config_defaultViewSettings2.density) === 'compact' || ((_config_defaultViewSettings3 = config.defaultViewSettings) === null || _config_defaultViewSettings3 === void 0 ? void 0 : _config_defaultViewSettings3.density) === 'comfortable' || ((_config_defaultViewSettings4 = config.defaultViewSettings) === null || _config_defaultViewSettings4 === void 0 ? void 0 : _config_defaultViewSettings4.density) === 'spacious' ? config.defaultViewSettings.density : 'comfortable');\n    const [activeActionMenu, setActiveActionMenu] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [visibleColumns, setVisibleColumns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(Array.isArray((_config_defaultViewSettings5 = config.defaultViewSettings) === null || _config_defaultViewSettings5 === void 0 ? void 0 : _config_defaultViewSettings5.visibleColumns) ? config.defaultViewSettings.visibleColumns : []);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showColumnSelector, setShowColumnSelector] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showWindowList, setShowWindowList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [gridColumns, setGridColumns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(3);\n    // Modal states\n    const [isCreateModalOpen, setIsCreateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isEditModalOpen, setIsEditModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [editingPost, setEditingPost] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Confirmation modal state\n    const [deleteConfirmation, setDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        isOpen: false,\n        post: null,\n        isBulkDelete: false,\n        bulkPosts: []\n    });\n    // Debounce search query\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BlogsManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"BlogsManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchQuery(searchQuery);\n                    setCurrentPage(1); // Reset to first page when searching\n                }\n            }[\"BlogsManagement.useEffect.timer\"], 300);\n            return ({\n                \"BlogsManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"BlogsManagement.useEffect\"];\n        }\n    }[\"BlogsManagement.useEffect\"], [\n        searchQuery\n    ]);\n    // Handle clicking outside to close action menu\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BlogsManagement.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"BlogsManagement.useEffect.handleClickOutside\": (event)=>{\n                    if (activeActionMenu) {\n                        const target = event.target;\n                        const actionMenu = document.querySelector('[data-post-id=\"'.concat(activeActionMenu, '\"] .action-menu'));\n                        const actionButton = document.querySelector('[data-post-id=\"'.concat(activeActionMenu, '\"] .action-button'));\n                        if (actionMenu && actionButton && !actionMenu.contains(target) && !actionButton.contains(target)) {\n                            setActiveActionMenu(null);\n                        }\n                    }\n                }\n            }[\"BlogsManagement.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"BlogsManagement.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"BlogsManagement.useEffect\"];\n        }\n    }[\"BlogsManagement.useEffect\"], [\n        activeActionMenu\n    ]);\n    // Fetch blog posts\n    const fetchBlogPosts = async function() {\n        let preserveFocus = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        setLoading(true);\n        if (!preserveFocus) {\n            showLoading('Loading Blog Posts', 'Retrieving blog posts...');\n        }\n        try {\n            const params = new URLSearchParams({\n                page: currentPage.toString(),\n                limit: (config.pageSize || 10).toString(),\n                search: debouncedSearchQuery,\n                sortBy,\n                sortOrder\n            });\n            // Add filters to params\n            Object.entries(filters).forEach((param)=>{\n                let [key, value] = param;\n                if (value) {\n                    params.append(key, value);\n                }\n            });\n            console.log('Fetching blog posts with params:', params.toString()); // Debug log\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"?\").concat(params));\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('API Error Response:', errorText);\n                throw new Error(\"HTTP error! status: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const data = await response.json();\n            console.log('Received blog posts data:', data); // Debug log\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to fetch blog posts');\n            }\n            setBlogPosts(data.posts || []);\n            setTotalPages(Math.ceil((data.totalCount || 0) / (config.pageSize || 10)));\n            setError(null); // Clear any previous errors on successful fetch\n            if (!preserveFocus) {\n                var _data_posts, _data_posts1;\n                showSuccess('Blog Posts Loaded', \"Loaded \".concat(((_data_posts = data.posts) === null || _data_posts === void 0 ? void 0 : _data_posts.length) || 0, \" blog post\").concat(((_data_posts1 = data.posts) === null || _data_posts1 === void 0 ? void 0 : _data_posts1.length) === 1 ? '' : 's'));\n            }\n        } catch (err) {\n            console.error('Error fetching blog posts:', err); // Debug log\n            setError(err instanceof Error ? err.message : 'Failed to fetch blog posts');\n            if (!preserveFocus) {\n                showError('Failed to Load Blog Posts', 'Unable to retrieve blog posts');\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BlogsManagement.useEffect\": ()=>{\n            // Preserve focus when searching\n            const isSearching = debouncedSearchQuery !== '';\n            fetchBlogPosts(isSearching);\n        }\n    }[\"BlogsManagement.useEffect\"], [\n        currentPage,\n        debouncedSearchQuery,\n        sortBy,\n        sortOrder,\n        filters\n    ]);\n    // Handle create\n    const handleCreate = async (formData)=>{\n        try {\n            showLoading('Creating Blog Post', 'Saving new blog post...');\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to create blog post (\".concat(response.status, \")\"));\n            }\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to create blog post');\n            }\n            setIsCreateModalOpen(false);\n            fetchBlogPosts();\n            showSuccess('Blog Post Created', '\"'.concat(formData.title || 'New blog post', '\" created successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to create blog post';\n            setError(errorMessage);\n            showError('Failed to Create Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Handle add button click\n    const handleAddClick = ()=>{\n        showInfo('Opening Create Form', 'Preparing to create a new blog post...');\n        setIsCreateModalOpen(true);\n    };\n    // Handle update\n    const handleUpdate = async (id, formData)=>{\n        try {\n            showLoading('Updating Blog Post', 'Saving changes...');\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to update blog post (\".concat(response.status, \")\"));\n            }\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to update blog post');\n            }\n            setIsEditModalOpen(false);\n            setEditingPost(null);\n            fetchBlogPosts();\n            showSuccess('Blog Post Updated', '\"'.concat(formData.title || 'Blog post', '\" updated successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to update blog post';\n            setError(errorMessage);\n            showError('Failed to Update Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Handle delete\n    const handleDelete = async (id)=>{\n        try {\n            const postToDelete = blogPosts.find((post)=>post.id === id);\n            showLoading('Deleting Blog Post', 'Removing \"'.concat((postToDelete === null || postToDelete === void 0 ? void 0 : postToDelete.title) || 'blog post', '\"...'));\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete blog post');\n            fetchBlogPosts();\n            showSuccess('Blog Post Deleted', '\"'.concat((postToDelete === null || postToDelete === void 0 ? void 0 : postToDelete.title) || 'Blog post', '\" deleted successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to delete blog post';\n            setError(errorMessage);\n            showError('Failed to Delete Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Handle duplicate\n    const handleDuplicate = async (post)=>{\n        try {\n            showLoading('Duplicating Blog Post', 'Creating copy of \"'.concat(post.title, '\"...'));\n            // Create duplicate data with modified title and slug\n            const duplicateData = {\n                title: \"\".concat(post.title, \" (Copy)\"),\n                slug: \"\".concat(post.slug, \"-copy-\").concat(Date.now()),\n                content: post.content,\n                excerpt: post.excerpt || '',\n                featuredImageUrl: post.featuredImageUrl || '',\n                authorId: post.authorId || '',\n                isPublished: false,\n                publishedAt: null,\n                categories: post.categories || '',\n                tags: post.tags || ''\n            };\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(duplicateData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to duplicate blog post (\".concat(response.status, \")\"));\n            }\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to duplicate blog post');\n            }\n            fetchBlogPosts();\n            showSuccess('Blog Post Duplicated', '\"'.concat(post.title, '\" duplicated successfully as \"').concat(duplicateData.title, '\"!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to duplicate blog post';\n            setError(errorMessage);\n            showError('Failed to Duplicate Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Handle archive\n    const handleArchive = async (post)=>{\n        try {\n            showLoading('Archiving Blog Post', 'Archiving \"'.concat(post.title, '\"...'));\n            // Archive by unpublishing and adding archive tag\n            const archiveData = {\n                isPublished: false,\n                tags: post.tags ? \"\".concat(post.tags, \",archived\") : 'archived',\n                categories: post.categories || '',\n                title: post.title,\n                content: post.content,\n                excerpt: post.excerpt || '',\n                featuredImageUrl: post.featuredImageUrl || '',\n                authorId: post.authorId || '',\n                slug: post.slug\n            };\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(post.id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(archiveData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to archive blog post (\".concat(response.status, \")\"));\n            }\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to archive blog post');\n            }\n            fetchBlogPosts();\n            showSuccess('Blog Post Archived', '\"'.concat(post.title, '\" archived successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to archive blog post';\n            setError(errorMessage);\n            showError('Failed to Archive Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Show delete confirmation\n    const showDeleteConfirmation = (post)=>{\n        setDeleteConfirmation({\n            isOpen: true,\n            post,\n            isBulkDelete: false,\n            bulkPosts: []\n        });\n    };\n    // Show bulk delete confirmation\n    const showBulkDeleteConfirmation = ()=>{\n        const postsToDelete = blogPosts.filter((post)=>selectedPosts.includes(post.id));\n        setDeleteConfirmation({\n            isOpen: true,\n            post: null,\n            isBulkDelete: true,\n            bulkPosts: postsToDelete\n        });\n    };\n    // Confirm delete\n    const confirmDelete = async ()=>{\n        try {\n            if (deleteConfirmation.isBulkDelete && deleteConfirmation.bulkPosts) {\n                // Bulk delete\n                const promises = deleteConfirmation.bulkPosts.map((post)=>fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(post.id), {\n                        method: 'DELETE'\n                    }));\n                await Promise.all(promises);\n                setSelectedPosts([]);\n                fetchBlogPosts();\n                showSuccess('Blog Posts Deleted', \"\".concat(deleteConfirmation.bulkPosts.length, \" blog post(s) deleted successfully!\"));\n            } else if (deleteConfirmation.post) {\n                // Single delete\n                await handleDelete(deleteConfirmation.post.id);\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to delete blog post(s)';\n            showError('Failed to Delete Blog Post(s)', errorMessage);\n        } finally{\n            setDeleteConfirmation({\n                isOpen: false,\n                post: null,\n                isBulkDelete: false,\n                bulkPosts: []\n            });\n        }\n    };\n    // Cancel delete\n    const cancelDelete = ()=>{\n        setDeleteConfirmation({\n            isOpen: false,\n            post: null,\n            isBulkDelete: false,\n            bulkPosts: []\n        });\n    };\n    // Handle bulk actions\n    const handleBulkAction = async (action)=>{\n        if (selectedPosts.length === 0) return;\n        setActionLoading(action);\n        try {\n            if (action === 'delete') {\n                showBulkDeleteConfirmation();\n                return;\n            }\n            showLoading(\"Bulk \".concat(action), \"Processing \".concat(selectedPosts.length, \" blog post\").concat(selectedPosts.length === 1 ? '' : 's', \"...\"));\n            const promises = selectedPosts.map(async (id)=>{\n                switch(action){\n                    case 'publish':\n                        return fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                            method: 'PUT',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                isPublished: true\n                            })\n                        });\n                    case 'unpublish':\n                        return fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                            method: 'PUT',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                isPublished: false\n                            })\n                        });\n                    case 'duplicate':\n                        const postToDuplicate = blogPosts.find((post)=>post.id === id);\n                        if (postToDuplicate) {\n                            const duplicateData = {\n                                title: \"\".concat(postToDuplicate.title, \" (Copy)\"),\n                                slug: \"\".concat(postToDuplicate.slug, \"-copy-\").concat(Date.now()),\n                                content: postToDuplicate.content,\n                                excerpt: postToDuplicate.excerpt || '',\n                                featuredImageUrl: postToDuplicate.featuredImageUrl || '',\n                                authorId: postToDuplicate.authorId || '',\n                                isPublished: false,\n                                publishedAt: null,\n                                categories: postToDuplicate.categories || '',\n                                tags: postToDuplicate.tags || ''\n                            };\n                            return fetch(\"/api/admin/\".concat(config.endpoint), {\n                                method: 'POST',\n                                headers: {\n                                    'Content-Type': 'application/json'\n                                },\n                                body: JSON.stringify(duplicateData)\n                            });\n                        }\n                        throw new Error(\"Post not found for duplication: \".concat(id));\n                    case 'archive':\n                        const postToArchive = blogPosts.find((post)=>post.id === id);\n                        if (postToArchive) {\n                            const archiveData = {\n                                isPublished: false,\n                                tags: postToArchive.tags ? \"\".concat(postToArchive.tags, \",archived\") : 'archived',\n                                categories: postToArchive.categories || '',\n                                title: postToArchive.title,\n                                content: postToArchive.content,\n                                excerpt: postToArchive.excerpt || '',\n                                featuredImageUrl: postToArchive.featuredImageUrl || '',\n                                authorId: postToArchive.authorId || '',\n                                slug: postToArchive.slug\n                            };\n                            return fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                                method: 'PUT',\n                                headers: {\n                                    'Content-Type': 'application/json'\n                                },\n                                body: JSON.stringify(archiveData)\n                            });\n                        }\n                        throw new Error(\"Post not found for archiving: \".concat(id));\n                    default:\n                        throw new Error(\"Unknown bulk action: \".concat(action));\n                }\n            });\n            await Promise.all(promises);\n            setSelectedPosts([]);\n            fetchBlogPosts();\n            showSuccess(\"Bulk \".concat(action, \" completed\"), \"\".concat(selectedPosts.length, \" blog post\").concat(selectedPosts.length === 1 ? '' : 's', \" \").concat(action, \"ed successfully!\"));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"Failed to \".concat(action, \" blog posts\");\n            setError(errorMessage);\n            showError(\"Failed to \".concat(action, \" blog posts\"), errorMessage);\n        } finally{\n            setActionLoading(null);\n        }\n    };\n    // Handle individual actions\n    const handleAction = async (action, item)=>{\n        setActionLoading(\"\".concat(action, \"-\").concat(item.id));\n        try {\n            switch(action){\n                case 'edit':\n                    showInfo('Opening Editor', 'Editing \"'.concat(item.title, '\"'));\n                    setEditingPost(item);\n                    setIsEditModalOpen(true);\n                    break;\n                case 'view':\n                    showInfo('Opening View', 'Viewing \"'.concat(item.title, '\"'));\n                    // TODO: Implement view functionality\n                    showInfo('View Blog Post', 'Opening \"'.concat(item.title, '\" in new tab'));\n                    break;\n                case 'delete':\n                    showInfo('Delete Confirmation', 'Preparing to delete \"'.concat(item.title, '\"'));\n                    showDeleteConfirmation(item);\n                    break;\n                case 'toggle-published':\n                    const newStatus = !item.isPublished;\n                    showLoading(newStatus ? 'Publishing Blog Post' : 'Unpublishing Blog Post', \"\".concat(newStatus ? 'Publishing' : 'Unpublishing', ' \"').concat(item.title, '\"...'));\n                    await handleUpdate(item.id, {\n                        isPublished: newStatus\n                    });\n                    showSuccess(newStatus ? 'Blog Post Published' : 'Blog Post Unpublished', '\"'.concat(item.title, '\" ').concat(newStatus ? 'published' : 'unpublished', \" successfully!\"));\n                    break;\n                case 'duplicate':\n                    await handleDuplicate(item);\n                    break;\n                case 'archive':\n                    await handleArchive(item);\n                    break;\n                default:\n                    console.warn(\"Unknown action: \".concat(action));\n            }\n        } finally{\n            setActionLoading(null);\n        }\n    };\n    // Handle sorting\n    const handleSort = (field)=>{\n        const newOrder = sortBy === field ? sortOrder === 'asc' ? 'desc' : 'asc' : 'asc';\n        const newField = sortBy === field ? field : field;\n        setSortBy(newField);\n        setSortOrder(newOrder);\n        setCurrentPage(1);\n        showInfo('Sorting Blog Posts', \"Sorting by \".concat(field, \" (\").concat(newOrder, \")\"));\n    };\n    // Handle selection\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedPosts(blogPosts.map((post)=>post.id));\n            showInfo('Selection Updated', \"Selected all \".concat(blogPosts.length, \" blog post\").concat(blogPosts.length === 1 ? '' : 's'));\n        } else {\n            setSelectedPosts([]);\n            showInfo('Selection Cleared', 'Deselected all blog posts');\n        }\n    };\n    const handleSelectPost = (id, checked)=>{\n        if (checked) {\n            setSelectedPosts([\n                ...selectedPosts,\n                id\n            ]);\n            const post = blogPosts.find((p)=>p.id === id);\n            showInfo('Post Selected', '\"'.concat((post === null || post === void 0 ? void 0 : post.title) || 'Blog post', '\" added to selection'));\n        } else {\n            setSelectedPosts(selectedPosts.filter((postId)=>postId !== id));\n            const post = blogPosts.find((p)=>p.id === id);\n            showInfo('Post Deselected', '\"'.concat((post === null || post === void 0 ? void 0 : post.title) || 'Blog post', '\" removed from selection'));\n        }\n    };\n    // Get visible fields for table\n    const getVisibleFields = ()=>{\n        if (visibleColumns.length > 0) {\n            var _config_fields;\n            return ((_config_fields = config.fields) === null || _config_fields === void 0 ? void 0 : _config_fields.filter((field)=>visibleColumns.includes(field.key))) || [];\n        }\n        return config.fields || [];\n    };\n    // Handle view mode change\n    const handleViewModeChange = (mode)=>{\n        setViewMode(mode);\n        showInfo('View Mode Changed', \"Switched to \".concat(mode, \" view\"));\n    };\n    // Handle density change\n    const handleDensityChange = (newDensity)=>{\n        setDensity(newDensity);\n        showInfo('Density Updated', \"Changed to \".concat(newDensity, \" density\"));\n    };\n    // Handle grid columns change\n    const handleGridColumnsChange = (columns)=>{\n        setGridColumns(columns);\n        showInfo('Grid Layout Updated', \"Changed to \".concat(columns, \" column\").concat(columns === 1 ? '' : 's', \" layout\"));\n    };\n    // Handle filter changes\n    const handleFilterChange = (key, value)=>{\n        const newFilters = {\n            ...filters\n        };\n        if (value) {\n            newFilters[key] = value;\n        } else {\n            delete newFilters[key];\n        }\n        setFilters(newFilters);\n        showInfo('Filter Applied', \"Filter updated: \".concat(key, \" = \").concat(value || 'all'));\n    };\n    // Close all action menus\n    const closeAllActionMenus = ()=>{\n        setActiveActionMenu(null);\n    };\n    // Grid Card Component\n    const GridCard = (param)=>{\n        let { post } = param;\n        const isSelected = selectedPosts.includes(post.id);\n        const classes = getGridDensityClasses();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            \"data-post-id\": post.id,\n            className: \"group relative bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 \".concat(isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : '', \" \").concat(classes.card),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"aspect-video bg-gray-100 rounded-t-lg overflow-hidden\",\n                    children: post.featuredImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: post.featuredImageUrl,\n                        alt: post.title,\n                        className: \"w-full h-full object-cover\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 956,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-12 h-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 963,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 962,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 954,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: classes.content,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"\".concat(classes.title, \" text-gray-900 line-clamp-2\"),\n                            children: post.title\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 971,\n                            columnNumber: 11\n                        }, this),\n                        post.excerpt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"\".concat(classes.excerpt, \" text-gray-600 line-clamp-3\"),\n                            children: post.excerpt\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 977,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: classes.meta,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex \".concat(classes.status, \" font-semibold rounded-full \").concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                        children: post.isPublished ? 'Published' : 'Draft'\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 986,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 985,\n                                    columnNumber: 17\n                                }, this),\n                                post.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap \".concat(classes.categories),\n                                    children: post.categories.split(',').slice(0, 2).map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex \".concat(classes.category, \" bg-blue-100 text-blue-800 rounded\"),\n                                            children: category.trim()\n                                        }, index, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 999,\n                                            columnNumber: 23\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 997,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 983,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between \".concat(density === 'compact' ? 'mt-3 pt-2' : density === 'spacious' ? 'mt-5 pt-4' : 'mt-4 pt-3', \" border-t border-gray-200\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: [\n                                        \"Updated \",\n                                        formatDate(post.updatedAt)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1009,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"action-button p-2 text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors\",\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            const isVisible = activeActionMenu === post.id;\n                                            if (isVisible) {\n                                                // Hide menu\n                                                setActiveActionMenu(null);\n                                            } else {\n                                                // Show menu (this will automatically hide any other open menu)\n                                                setActiveActionMenu(post.id);\n                                            }\n                                        },\n                                        title: \"Show Actions\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1030,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1015,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1014,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1008,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"action-menu absolute bg-white rounded-lg border border-gray-200 shadow-lg flex flex-col items-center justify-center transition-all duration-200 z-50 \".concat(density === 'compact' ? 'top-1 right-1 bottom-1 w-12 space-y-1.5 px-1' : density === 'spacious' ? 'top-3 right-3 bottom-3 w-16 space-y-3 px-2' : 'top-2 right-2 bottom-2 w-14 space-y-2 px-1.5'),\n                            style: {\n                                opacity: activeActionMenu === post.id ? '1' : '0',\n                                transform: activeActionMenu === post.id ? 'translateX(0)' : 'translateX(100%)',\n                                pointerEvents: activeActionMenu === post.id ? 'auto' : 'none'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeAllActionMenus();\n                                        handleAction('edit', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 border border-blue-500 hover:border-blue-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : 'w-10 h-10'),\n                                    title: \"Edit Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1059,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1048,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeAllActionMenus();\n                                        handleAction('view', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-indigo-600 hover:bg-indigo-700 border border-indigo-500 hover:border-indigo-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : 'w-10 h-10'),\n                                    title: \"View Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1074,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1063,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeAllActionMenus();\n                                        handleAction('toggle-published', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center \".concat(post.isPublished ? 'bg-orange-600 hover:bg-orange-700 border-orange-500 hover:border-orange-600' : 'bg-green-600 hover:bg-green-700 border-green-500 hover:border-green-600', \" border text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \").concat(density === 'compact' ? 'w-8 h-8' : 'w-10 h-10'),\n                                    title: post.isPublished ? 'Unpublish Blog Post' : 'Publish Blog Post',\n                                    children: post.isPublished ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1094,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1096,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1078,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeAllActionMenus();\n                                        handleAction('duplicate', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-purple-600 hover:bg-purple-700 border border-purple-500 hover:border-purple-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : 'w-10 h-10'),\n                                    title: \"Duplicate Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1112,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeAllActionMenus();\n                                        handleAction('archive', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-yellow-600 hover:bg-yellow-700 border border-yellow-500 hover:border-yellow-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : 'w-10 h-10'),\n                                    title: \"Archive Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1127,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1116,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeAllActionMenus();\n                                        handleAction('delete', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-red-600 hover:bg-red-700 border border-red-500 hover:border-red-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : 'w-10 h-10'),\n                                    title: \"Delete Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1142,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1131,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1036,\n                            columnNumber: 11\n                        }, this),\n                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 left-2 \".concat(classes.checkbox),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                checked: isSelected,\n                                onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white \".concat(classes.checkbox),\n                                style: {\n                                    backgroundColor: 'white'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1150,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1149,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 969,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 947,\n            columnNumber: 7\n        }, this);\n    };\n    // Grid density styling helper\n    const getGridDensityClasses = ()=>{\n        const baseClasses = {\n            container: {\n                compact: 'p-1',\n                comfortable: 'p-3',\n                spacious: 'p-4'\n            },\n            grid: {\n                compact: 'gap-2',\n                comfortable: 'gap-3',\n                spacious: 'gap-4'\n            },\n            card: {\n                compact: 'p-1',\n                comfortable: 'p-2',\n                spacious: 'p-3'\n            },\n            content: {\n                compact: 'p-1',\n                comfortable: 'p-2',\n                spacious: 'p-3'\n            },\n            title: {\n                compact: 'text-sm font-semibold mb-0.5',\n                comfortable: 'text-lg font-semibold mb-1',\n                spacious: 'text-xl font-semibold mb-2'\n            },\n            excerpt: {\n                compact: 'text-xs mb-1',\n                comfortable: 'text-sm mb-1.5',\n                spacious: 'text-base mb-2'\n            },\n            meta: {\n                compact: 'space-y-0.5 mb-1',\n                comfortable: 'space-y-1 mb-2',\n                spacious: 'space-y-2 mb-3'\n            },\n            status: {\n                compact: 'px-1 py-0.5 text-xs',\n                comfortable: 'px-1.5 py-0.5 text-xs',\n                spacious: 'px-2 py-1 text-sm'\n            },\n            date: {\n                compact: 'text-xs',\n                comfortable: 'text-xs',\n                spacious: 'text-sm'\n            },\n            categories: {\n                compact: 'gap-0.5',\n                comfortable: 'gap-0.5',\n                spacious: 'gap-1'\n            },\n            category: {\n                compact: 'px-1 py-0.5 text-xs',\n                comfortable: 'px-1.5 py-0.5 text-xs',\n                spacious: 'px-2 py-1 text-sm'\n            },\n            actions: {\n                compact: 'pt-1',\n                comfortable: 'pt-1.5',\n                spacious: 'pt-2'\n            },\n            buttons: {\n                compact: 'space-x-0.5',\n                comfortable: 'space-x-1',\n                spacious: 'space-x-2'\n            },\n            button: {\n                compact: 'p-0.5',\n                comfortable: 'p-1',\n                spacious: 'p-1.5'\n            },\n            icon: {\n                compact: 'w-2.5 h-2.5',\n                comfortable: 'w-3 h-3',\n                spacious: 'w-4 h-4'\n            },\n            checkbox: {\n                compact: 'h-2.5 w-2.5',\n                comfortable: 'h-3 w-3',\n                spacious: 'h-4 w-4'\n            }\n        };\n        return {\n            container: baseClasses.container[density],\n            grid: baseClasses.grid[density],\n            card: baseClasses.card[density],\n            content: baseClasses.content[density],\n            title: baseClasses.title[density],\n            excerpt: baseClasses.excerpt[density],\n            meta: baseClasses.meta[density],\n            status: baseClasses.status[density],\n            date: baseClasses.date[density],\n            categories: baseClasses.categories[density],\n            category: baseClasses.category[density],\n            actions: baseClasses.actions[density],\n            buttons: baseClasses.buttons[density],\n            button: baseClasses.button[density],\n            icon: baseClasses.icon[density],\n            checkbox: baseClasses.checkbox[density]\n        };\n    };\n    // Format date\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString();\n    };\n    // Truncate text\n    const truncateText = (text, maxLength)=>{\n        if (text.length <= maxLength) return text;\n        return text.substring(0, maxLength) + '...';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"h-full flex flex-col space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"d5ed160e6f329c5c\",\n                children: \".action-menu{transition:all.2s ease-in-out}@media(min-width:1025px){.group:hover .action-menu{opacity:1!important;transform:translatex(0)!important;pointer-events:auto!important}}.density-compact th,.density-compact td{padding-top:4px!important;padding-bottom:4px!important}.density-comfortable th,.density-comfortable td{padding-top:16px!important;padding-bottom:16px!important}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative rounded-lg shadow-sm border border-gray-200/50 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-indigo-50/20\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 1308,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative p-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-14 w-14 text-lime-600 -mt-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1313,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-3xl font-bold text-gray-900 mt-2\",\n                                                    children: \"Blog Management\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1315,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm font-medium text-gray-600\",\n                                                    children: \"Create, edit, and manage your blog content.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1318,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1314,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1312,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"hidden lg:flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAddClick,\n                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors shadow-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1329,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Add Blog Post\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1325,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1324,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1311,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 1310,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 1307,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                role: \"main\",\n                \"aria-label\": \"Blog management section\",\n                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex-1 rounded-lg shadow-sm border border-gray-200/50 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        paddingBottom: '0'\n                    },\n                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"space-y-3 p-2 bg-white rounded-lg border border-gray-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex flex-col space-y-3 lg:hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1352,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"Search blog posts...\",\n                                                        value: searchQuery,\n                                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"pl-10 pr-4 py-2.5 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1353,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1351,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1350,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-0.5 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleViewModeChange('list'),\n                                                            title: \"List view\",\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 \".concat(viewMode === 'list' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1376,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-xs font-medium hidden xs:inline\",\n                                                                    children: \"List\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1377,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1367,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleViewModeChange('grid'),\n                                                            title: \"Grid view\",\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 \".concat(viewMode === 'grid' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1388,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-xs font-medium hidden xs:inline\",\n                                                                    children: \"Grid\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1389,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1379,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1366,\n                                                    columnNumber: 15\n                                                }, this),\n                                                viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-0.5 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-xs font-medium text-gray-700 px-1\",\n                                                            children: \"Col:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1396,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center gap-0.5 flex-1\",\n                                                            children: [\n                                                                1,\n                                                                2,\n                                                                3,\n                                                                4\n                                                            ].map((num)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(num),\n                                                                    title: \"\".concat(num, \" column\").concat(num > 1 ? 's' : ''),\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex-1 px-1.5 py-1 rounded text-xs font-medium \".concat(gridColumns === num ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: num\n                                                                }, num, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1399,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1397,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1395,\n                                                    columnNumber: 17\n                                                }, this),\n                                                viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowColumnSelector(!showColumnSelector),\n                                                        title: \"Columns\",\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1424,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"hidden xs:inline\",\n                                                                children: \"Col\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1425,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-3 w-3 ml-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1426,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1419,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1418,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowFilters(!showFilters),\n                                                        title: \"Filters\",\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium rounded-lg border \".concat(showFilters || Object.keys(filters).some((key)=>filters[key]) ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1442,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"hidden xs:inline\",\n                                                                children: \"Filter\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1443,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            Object.keys(filters).some((key)=>filters[key]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"ml-0.5 inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                children: Object.values(filters).filter(Boolean).length\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1445,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1433,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1432,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowWindowList(!showWindowList),\n                                                        title: \"Density\",\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1459,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"hidden xs:inline\",\n                                                                children: density.charAt(0).toUpperCase() + density.slice(1)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1460,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-3 w-3 ml-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1461,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1454,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1453,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1364,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1348,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"hidden lg:flex items-center justify-between gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center gap-3 flex-1 max-w-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1473,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"Search blog posts by title, content, excerpt...\",\n                                                            value: searchQuery,\n                                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1474,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1472,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowFilters(!showFilters),\n                                                            title: \"Show/hide filters\",\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(showFilters || Object.keys(filters).some((key)=>filters[key]) ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1494,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Filters\",\n                                                                Object.keys(filters).some((key)=>filters[key]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                    children: Object.values(filters).filter(Boolean).length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1497,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1501,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1485,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"absolute top-full right-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center justify-between mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                                                children: \"Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1509,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>setShowFilters(false),\n                                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-gray-400 hover:text-gray-600\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1514,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1510,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1508,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"space-y-4\",\n                                                                        children: (_config_filters = config.filters) === null || _config_filters === void 0 ? void 0 : _config_filters.map((filter)=>{\n                                                                            var _filter_options;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-d5ed160e6f329c5c\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"block text-xs font-medium text-gray-700 mb-2\",\n                                                                                        children: filter.label\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1521,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                        value: filters[filter.key] || '',\n                                                                                        onChange: (e)=>{\n                                                                                            const newFilters = {\n                                                                                                ...filters\n                                                                                            };\n                                                                                            if (e.target.value) {\n                                                                                                newFilters[filter.key] = e.target.value;\n                                                                                            } else {\n                                                                                                delete newFilters[filter.key];\n                                                                                            }\n                                                                                            setFilters(newFilters);\n                                                                                        },\n                                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                                                        children: (_filter_options = filter.options) === null || _filter_options === void 0 ? void 0 : _filter_options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: option.value,\n                                                                                                className: \"jsx-d5ed160e6f329c5c\",\n                                                                                                children: option.label\n                                                                                            }, option.value, false, {\n                                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                                lineNumber: 1538,\n                                                                                                columnNumber: 37\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1524,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, filter.key, true, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1520,\n                                                                                columnNumber: 31\n                                                                            }, this);\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1518,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex justify-end mt-4 pt-4 border-t border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                setFilters({});\n                                                                                setShowFilters(false);\n                                                                            },\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg\",\n                                                                            children: \"Clear All\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1548,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1547,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1507,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1506,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1484,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1471,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm font-medium text-gray-700\",\n                                                            children: \"View:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1568,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-1 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleViewModeChange('list'),\n                                                                    title: \"List view\",\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-3 py-2 rounded-md flex items-center gap-2 \".concat(viewMode === 'list' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-5 w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1579,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm font-medium\",\n                                                                            children: \"List\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1580,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1570,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleViewModeChange('grid'),\n                                                                    title: \"Grid view\",\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-3 py-2 rounded-md flex items-center gap-2 \".concat(viewMode === 'grid' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-5 w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1591,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm font-medium\",\n                                                                            children: \"Grid\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1592,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1582,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1569,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1567,\n                                                    columnNumber: 19\n                                                }, this),\n                                                viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm font-medium text-gray-700\",\n                                                            children: \"Columns:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1600,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-1 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(1),\n                                                                    title: \"1 column\",\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 1 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1602,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(2),\n                                                                    title: \"2 columns\",\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 2 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1613,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(3),\n                                                                    title: \"3 columns\",\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 3 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1624,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(4),\n                                                                    title: \"4 columns\",\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 4 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1635,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1601,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1599,\n                                                    columnNumber: 21\n                                                }, this),\n                                                viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowColumnSelector(!showColumnSelector),\n                                                            title: \"Select columns to display\",\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1658,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Columns\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1660,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1653,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        showColumnSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-xs font-medium text-gray-500 uppercase tracking-wide mb-2\",\n                                                                        children: \"Show Columns\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1667,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    config.columns.map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-2 py-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: visibleColumns.includes(column.key),\n                                                                                    onChange: (e)=>{\n                                                                                        if (e.target.checked) {\n                                                                                            setVisibleColumns((prev)=>[\n                                                                                                    ...prev,\n                                                                                                    column.key\n                                                                                                ]);\n                                                                                        } else {\n                                                                                            setVisibleColumns((prev)=>prev.filter((col)=>col !== column.key));\n                                                                                        }\n                                                                                    },\n                                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1670,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm text-gray-700\",\n                                                                                    children: column.label\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1682,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, column.key, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1669,\n                                                                            columnNumber: 31\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1666,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1665,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1652,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowWindowList(!showWindowList),\n                                                            title: \"Select density\",\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1700,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                density.charAt(0).toUpperCase() + density.slice(1),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1702,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1695,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showWindowList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"absolute top-full left-0 mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"p-1\",\n                                                                children: [\n                                                                    'compact',\n                                                                    'comfortable',\n                                                                    'spacious'\n                                                                ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            handleDensityChange(option);\n                                                                            setShowWindowList(false);\n                                                                        },\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 \".concat(density === option ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'),\n                                                                        children: option.charAt(0).toUpperCase() + option.slice(1)\n                                                                    }, option, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1710,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1708,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1707,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1694,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1565,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1469,\n                                    columnNumber: 15\n                                }, this),\n                                showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                    children: \"Filters\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1736,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowFilters(false),\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-gray-400 hover:text-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1741,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1737,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1735,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"space-y-4\",\n                                            children: (_config_filters1 = config.filters) === null || _config_filters1 === void 0 ? void 0 : _config_filters1.map((filter)=>{\n                                                var _filter_options;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"block text-xs font-medium text-gray-700 mb-2\",\n                                                            children: filter.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1748,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: filters[filter.key] || '',\n                                                            onChange: (e)=>{\n                                                                const newFilters = {\n                                                                    ...filters\n                                                                };\n                                                                if (e.target.value) {\n                                                                    newFilters[filter.key] = e.target.value;\n                                                                } else {\n                                                                    delete newFilters[filter.key];\n                                                                }\n                                                                setFilters(newFilters);\n                                                            },\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                            children: (_filter_options = filter.options) === null || _filter_options === void 0 ? void 0 : _filter_options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: option.value,\n                                                                    className: \"jsx-d5ed160e6f329c5c\",\n                                                                    children: option.label\n                                                                }, option.value, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1765,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1751,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, filter.key, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1747,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1745,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex justify-end mt-4 pt-4 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setFilters({});\n                                                    setShowFilters(false);\n                                                },\n                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg\",\n                                                children: \"Clear All\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1775,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1774,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1734,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1346,\n                            columnNumber: 11\n                        }, this),\n                        config.enableBulkActions && selectedPosts.length > 0 && (viewMode === 'list' || viewMode === 'grid') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"bg-blue-50 border border-blue-200 rounded-lg px-4 py-2 shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-blue-600 font-semibold text-xs\",\n                                                            children: selectedPosts.length\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1796,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1795,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-xs font-medium text-blue-900\",\n                                                        children: [\n                                                            \"blog post\",\n                                                            selectedPosts.length === 1 ? '' : 's',\n                                                            \" selected\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1800,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1794,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleBulkAction('publish'),\n                                                        title: \"Publish selected blog posts\",\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 border border-green-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1812,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Publish\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1807,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleBulkAction('unpublish'),\n                                                        title: \"Unpublish selected blog posts\",\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1821,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Unpublish\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1816,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleBulkAction('duplicate'),\n                                                        title: \"Duplicate selected blog posts\",\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-purple-700 bg-purple-100 hover:bg-purple-200 border border-purple-300 rounded focus:outline-none focus:ring-1 focus:ring-purple-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1830,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Duplicate\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1825,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleBulkAction('archive'),\n                                                        title: \"Archive selected blog posts\",\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 hover:bg-yellow-200 border border-yellow-300 rounded focus:outline-none focus:ring-1 focus:ring-yellow-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1839,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Archive\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1834,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>showBulkDeleteConfirmation(),\n                                                        title: \"Delete selected blog posts\",\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 border border-red-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1848,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Delete\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1843,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1806,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1793,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-1.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedPosts([]),\n                                            title: \"Clear selection\",\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gray-500 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1861,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Clear\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1856,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1855,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1792,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1791,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-5 w-5 text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1875,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1874,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm font-medium text-red-800\",\n                                                    children: \"Error\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1878,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"mt-2 text-sm text-red-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-d5ed160e6f329c5c\",\n                                                        children: error\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1880,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1879,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setError(null);\n                                                            fetchBlogPosts();\n                                                        },\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200\",\n                                                        children: \"Try Again\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1883,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1882,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1877,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1873,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1872,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1871,\n                            columnNumber: 15\n                        }, this),\n                        loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-6 py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1903,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"ml-3 text-gray-600\",\n                                        children: \"Loading blog posts...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1904,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1902,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1901,\n                            columnNumber: 15\n                        }, this),\n                        !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-d5ed160e6f329c5c\",\n                            children: blogPosts.length === 0 ? /* Empty State */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"p-12 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"mx-auto h-12 w-12 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1915,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"mt-2 text-sm font-medium text-gray-900\",\n                                        children: \"No blog posts found\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1916,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"mt-1 text-sm text-gray-500\",\n                                        children: debouncedSearchQuery ? 'Try adjusting your search terms.' : 'Get started by creating your first blog post.'\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1917,\n                                        columnNumber: 21\n                                    }, this),\n                                    ((_config_permissions = config.permissions) === null || _config_permissions === void 0 ? void 0 : _config_permissions.create) && !debouncedSearchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleAddClick,\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: \"Add Blog Post\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1922,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1921,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1914,\n                                columnNumber: 19\n                            }, this) : viewMode === 'list' ? /* Table View */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"min-w-full divide-y divide-gray-200 density-\".concat(density),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"bg-gray-200 border-b border-gray-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\",\n                                                    children: [\n                                                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            scope: \"col\",\n                                                            style: {\n                                                                width: '6px'\n                                                            },\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative pl-2 py-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: selectedPosts.length === blogPosts.length && blogPosts.length > 0,\n                                                                onChange: (e)=>handleSelectAll(e.target.checked),\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-blue-600 focus:ring-blue-500 border-gray-300 rounded \".concat(density === 'compact' ? 'h-4 w-4' : 'h-4 w-4')\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1941,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1940,\n                                                            columnNumber: 31\n                                                        }, this),\n                                                        getVisibleFields().map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                scope: \"col\",\n                                                                onClick: ()=>handleSort(field.key),\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-6 py-2 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 text-xs\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-d5ed160e6f329c5c\",\n                                                                            children: field.label\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1961,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        sortBy === field.key ? sortOrder === 'asc' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-black\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1964,\n                                                                            columnNumber: 39\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-black\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1966,\n                                                                            columnNumber: 39\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1969,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1960,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, field.key, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1954,\n                                                                columnNumber: 31\n                                                            }, this)),\n                                                        config.actions && config.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            scope: \"col\",\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-6 py-2 text-right font-medium text-gray-900 uppercase tracking-wider text-xs min-w-[120px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\",\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1978,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1977,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1937,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1936,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"bg-white divide-y divide-gray-200\",\n                                                children: blogPosts.map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"hover:bg-gray-50 \".concat(selectedPosts.includes(post.id) ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''),\n                                                        children: [\n                                                            config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: selectedPosts.includes(post.id),\n                                                                    onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                                                    style: {\n                                                                        backgroundColor: 'white'\n                                                                    },\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1991,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1990,\n                                                                columnNumber: 33\n                                                            }, this),\n                                                            getVisibleFields().map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-6 whitespace-nowrap\",\n                                                                    children: field.key === 'title' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BlogAvatar, {\n                                                                                title: post.title,\n                                                                                featuredImageUrl: post.featuredImageUrl,\n                                                                                size: \"sm\",\n                                                                                className: \"mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 2006,\n                                                                                columnNumber: 39\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-d5ed160e6f329c5c\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                                                        children: post.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 2013,\n                                                                                        columnNumber: 41\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm text-gray-500\",\n                                                                                        children: post.slug\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 2014,\n                                                                                        columnNumber: 41\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 2012,\n                                                                                columnNumber: 39\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 2005,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'isPublished' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                        children: post.isPublished ? 'Published' : 'Draft'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 2018,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'excerpt' || field.key === 'content' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        title: post[field.key],\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm text-gray-900 max-w-xs truncate\",\n                                                                        children: truncateText(post[field.key] || '', 50)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 2026,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'tags' || field.key === 'categories' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm text-gray-900\",\n                                                                        children: post[field.key] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex flex-wrap gap-1\",\n                                                                            children: [\n                                                                                post[field.key].split(',').slice(0, 2).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\",\n                                                                                        children: tag.trim()\n                                                                                    }, index, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 2034,\n                                                                                        columnNumber: 45\n                                                                                    }, this)),\n                                                                                post[field.key].split(',').length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-xs text-gray-500\",\n                                                                                    children: [\n                                                                                        \"+\",\n                                                                                        post[field.key].split(',').length - 2,\n                                                                                        \" more\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2039,\n                                                                                    columnNumber: 45\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2032,\n                                                                            columnNumber: 41\n                                                                        }, this) : '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 2030,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'createdAt' || field.key === 'updatedAt' || field.key === 'publishedAt' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm text-gray-500\",\n                                                                        children: post[field.key] ? formatDate(post[field.key]) : '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 2045,\n                                                                        columnNumber: 37\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm text-gray-900\",\n                                                                        children: post[field.key] || '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 2049,\n                                                                        columnNumber: 37\n                                                                    }, this)\n                                                                }, field.key, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 2003,\n                                                                    columnNumber: 33\n                                                                }, this)),\n                                                            config.actions && config.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-6 whitespace-nowrap text-right text-sm font-medium\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-2\",\n                                                                    children: config.actions.map((action)=>{\n                                                                        const isLoading = actionLoading === \"\".concat(action.action, \"-\").concat(post.id);\n                                                                        const IconComponent = action.icon === 'EyeIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"] : action.icon === 'PencilIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"] : action.icon === 'PowerIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"] : action.icon === 'TrashIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"] : _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleAction(action.action, post),\n                                                                            disabled: isLoading,\n                                                                            title: action.tooltip,\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"p-1 rounded-md transition-colors \".concat(action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' : action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' : action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' : action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' : 'text-gray-600 hover:bg-gray-50', \" \").concat(isLoading ? 'opacity-50 cursor-not-allowed' : ''),\n                                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"animate-spin rounded-full h-4 w-4 border-b-2 border-current\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 2082,\n                                                                                columnNumber: 45\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 2084,\n                                                                                columnNumber: 45\n                                                                            }, this)\n                                                                        }, action.action, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2068,\n                                                                            columnNumber: 41\n                                                                        }, this);\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 2059,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 2058,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        ]\n                                                    }, post.id, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1985,\n                                                        columnNumber: 29\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1983,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1935,\n                                        columnNumber: 23\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1934,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1933,\n                                columnNumber: 19\n                            }, this) : viewMode === 'grid' ? /* Grid View - Restructured */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + (getGridDensityClasses().container || \"\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"grid \".concat(getGridDensityClasses().grid, \" \").concat(gridColumns === 1 ? 'grid-cols-1' : gridColumns === 2 ? 'grid-cols-1 md:grid-cols-2' : gridColumns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'),\n                                    children: blogPosts.map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridCard, {\n                                            post: post\n                                        }, post.id, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 2108,\n                                            columnNumber: 25\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 2101,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 2100,\n                                columnNumber: 19\n                            }, this) : /* Card View */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"space-y-4\",\n                                    children: blogPosts.map((post)=>{\n                                        var _config_actions;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow \".concat(selectedPosts.includes(post.id) ? 'ring-2 ring-blue-500 bg-blue-50' : ''),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-48 h-32 bg-gray-100 rounded-l-lg overflow-hidden flex-shrink-0\",\n                                                        children: post.featuredImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: post.featuredImageUrl,\n                                                            alt: post.title,\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-full h-full object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 2124,\n                                                            columnNumber: 33\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-full h-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-8 h-8 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 2131,\n                                                                columnNumber: 35\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 2130,\n                                                            columnNumber: 33\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 2122,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex-1 p-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-start justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-lg font-semibold text-gray-900 mb-1\",\n                                                                            children: post.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2141,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm text-gray-500 mb-2\",\n                                                                            children: post.slug\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2144,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        post.excerpt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm text-gray-600 mb-3 line-clamp-2\",\n                                                                            children: post.excerpt\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2150,\n                                                                            columnNumber: 37\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-4 text-sm text-gray-500\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                                    children: post.isPublished ? 'Published' : 'Draft'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2157,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-d5ed160e6f329c5c\",\n                                                                                    children: [\n                                                                                        \"Updated: \",\n                                                                                        formatDate(post.updatedAt)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2164,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                post.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-d5ed160e6f329c5c\",\n                                                                                    children: [\n                                                                                        \"Category: \",\n                                                                                        post.categories.split(',')[0].trim()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2166,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2156,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 2139,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-2 ml-4\",\n                                                                    children: [\n                                                                        (_config_actions = config.actions) === null || _config_actions === void 0 ? void 0 : _config_actions.map((action)=>{\n                                                                            const isLoading = actionLoading === \"\".concat(action.action, \"-\").concat(post.id);\n                                                                            const IconComponent = action.icon === 'EyeIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"] : action.icon === 'PencilIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"] : action.icon === 'PowerIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"] : action.icon === 'TrashIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"] : _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleAction(action.action, post),\n                                                                                disabled: isLoading,\n                                                                                title: action.tooltip,\n                                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"p-2 rounded-md transition-colors \".concat(action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' : action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' : action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' : action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' : 'text-gray-600 hover:bg-gray-50', \" \").concat(isLoading ? 'opacity-50 cursor-not-allowed' : ''),\n                                                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"animate-spin rounded-full h-4 w-4 border-b-2 border-current\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2195,\n                                                                                    columnNumber: 43\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2197,\n                                                                                    columnNumber: 43\n                                                                                }, this)\n                                                                            }, action.action, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 2181,\n                                                                                columnNumber: 39\n                                                                            }, this);\n                                                                        }),\n                                                                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: selectedPosts.includes(post.id),\n                                                                            onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                                                            style: {\n                                                                                backgroundColor: 'white'\n                                                                            },\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2205,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 2172,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 2138,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 2137,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 2120,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, post.id, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 2117,\n                                            columnNumber: 25\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 2115,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 2114,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1911,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 1344,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 1339,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_modal__WEBPACK_IMPORTED_MODULE_3__.BlogModal, {\n                isOpen: isCreateModalOpen,\n                onClose: ()=>setIsCreateModalOpen(false),\n                onSubmit: handleCreate,\n                title: \"Create New Blog Post\"\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 2228,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_modal__WEBPACK_IMPORTED_MODULE_3__.BlogModal, {\n                isOpen: isEditModalOpen,\n                onClose: ()=>{\n                    setIsEditModalOpen(false);\n                    setEditingPost(null);\n                },\n                onSubmit: async (formData)=>{\n                    if (editingPost) {\n                        await handleUpdate(editingPost.id, formData);\n                    }\n                },\n                title: \"Edit Blog Post\",\n                initialData: editingPost !== null && editingPost !== void 0 ? editingPost : undefined\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 2235,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_confirmation_modal__WEBPACK_IMPORTED_MODULE_5__.ConfirmationModal, {\n                isOpen: deleteConfirmation.isOpen,\n                title: \"Delete Confirmation\",\n                message: \"Are you sure you want to delete this blog post?\",\n                details: (()=>{\n                    if (deleteConfirmation.isBulkDelete) {\n                        var _deleteConfirmation_bulkPosts;\n                        const count = ((_deleteConfirmation_bulkPosts = deleteConfirmation.bulkPosts) === null || _deleteConfirmation_bulkPosts === void 0 ? void 0 : _deleteConfirmation_bulkPosts.length) || 0;\n                        return \"This action will permanently delete \".concat(count, \" blog post\").concat(count === 1 ? '' : 's', \". This cannot be undone.\");\n                    }\n                    const post = deleteConfirmation.post;\n                    return 'This action will permanently delete \"'.concat((post === null || post === void 0 ? void 0 : post.title) || 'this blog post', '\". This cannot be undone.');\n                })(),\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                onConfirm: confirmDelete,\n                onCancel: cancelDelete,\n                type: \"danger\",\n                showVerification: true,\n                verificationData: {\n                    canDelete: true,\n                    reason: deleteConfirmation.isBulkDelete ? \"\".concat(((_deleteConfirmation_bulkPosts = deleteConfirmation.bulkPosts) === null || _deleteConfirmation_bulkPosts === void 0 ? void 0 : _deleteConfirmation_bulkPosts.length) || 0, \" blog post\").concat((((_deleteConfirmation_bulkPosts1 = deleteConfirmation.bulkPosts) === null || _deleteConfirmation_bulkPosts1 === void 0 ? void 0 : _deleteConfirmation_bulkPosts1.length) || 0) === 1 ? '' : 's', \" selected for deletion\") : 'Blog post \"'.concat(((_deleteConfirmation_post = deleteConfirmation.post) === null || _deleteConfirmation_post === void 0 ? void 0 : _deleteConfirmation_post.title) || 'Unknown', '\" ready for deletion')\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 2251,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n        lineNumber: 1281,\n        columnNumber: 5\n    }, this);\n}\n_s1(BlogsManagement, \"08CoGxmgR5Cz6DPM47nmmICY3ZU=\", false, function() {\n    return [\n        _components_providers_notification_provider__WEBPACK_IMPORTED_MODULE_4__.useNotifications\n    ];\n});\n_c1 = BlogsManagement;\nvar _c, _c1;\n$RefreshReg$(_c, \"BlogAvatar\");\n$RefreshReg$(_c1, \"BlogsManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/blog/blogs-management.tsx\n"));

/***/ })

});