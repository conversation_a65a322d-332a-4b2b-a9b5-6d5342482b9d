"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/blog/page",{

/***/ "(app-pages-browser)/./src/components/admin/blog/blogs-management.tsx":
/*!********************************************************!*\
  !*** ./src/components/admin/blog/blogs-management.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlogsManagement: () => (/* binding */ BlogsManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EllipsisVerticalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PowerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentDuplicateIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArchiveBoxIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js\");\n/* harmony import */ var _blog_modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./blog-modal */ \"(app-pages-browser)/./src/components/admin/blog/blog-modal.tsx\");\n/* harmony import */ var _components_providers_notification_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/notification-provider */ \"(app-pages-browser)/./src/components/providers/notification-provider.tsx\");\n/* harmony import */ var _shared_confirmation_modal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/confirmation-modal */ \"(app-pages-browser)/./src/components/admin/shared/confirmation-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ BlogsManagement auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nfunction BlogAvatar(param) {\n    let { title, featuredImageUrl, size = 'md', className = '', style = {} } = param;\n    _s();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [imageLoading, setImageLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // Size configurations\n    const sizeClasses = {\n        xs: 'w-6 h-6',\n        sm: 'w-8 h-8',\n        md: 'w-12 h-12',\n        lg: 'w-16 h-16',\n        xl: 'w-24 h-24',\n        'full-height': 'w-full h-full'\n    };\n    const textSizes = {\n        xs: 'text-xs',\n        sm: 'text-sm',\n        md: 'text-base',\n        lg: 'text-lg',\n        xl: 'text-xl',\n        'full-height': 'text-4xl'\n    };\n    // Generate initials from blog title\n    const getInitials = (title)=>{\n        return title.split(' ').map((word)=>word.charAt(0)).join('').toUpperCase().slice(0, 2);\n    };\n    // Generate a consistent color based on the blog title\n    const getBackgroundColor = (title)=>{\n        const colors = [\n            'bg-blue-500',\n            'bg-green-500',\n            'bg-purple-500',\n            'bg-pink-500',\n            'bg-indigo-500',\n            'bg-yellow-500',\n            'bg-red-500',\n            'bg-teal-500',\n            'bg-orange-500',\n            'bg-cyan-500'\n        ];\n        let hash = 0;\n        for(let i = 0; i < title.length; i++){\n            hash = title.charCodeAt(i) + ((hash << 5) - hash);\n        }\n        return colors[Math.abs(hash) % colors.length];\n    };\n    const handleImageLoad = ()=>{\n        setImageLoading(false);\n        setImageError(false);\n    };\n    const handleImageError = ()=>{\n        setImageLoading(false);\n        setImageError(true);\n    };\n    const baseClasses = \"\\n    \".concat(sizeClasses[size], \" \\n    rounded-lg \\n    flex \\n    items-center \\n    justify-center \\n    overflow-hidden \\n    \").concat(size === 'full-height' ? 'min-h-[320px]' : '', \"\\n    \").concat(className, \"\\n  \");\n    // If we have a valid featured image URL and no error, show the image\n    if (featuredImageUrl && !imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(baseClasses, \" bg-gray-100 relative\"),\n            style: style,\n            children: [\n                imageLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 w-6 h-6\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: featuredImageUrl,\n                    alt: \"\".concat(title, \" featured image\"),\n                    className: \"\\n            w-full h-full object-cover\\n            \".concat(imageLoading ? 'opacity-0' : 'opacity-100', \"\\n            transition-opacity duration-200\\n          \"),\n                    onLoad: handleImageLoad,\n                    onError: handleImageError\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 271,\n            columnNumber: 7\n        }, this);\n    }\n    // Fallback: Show initials with colored background\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\\n        \".concat(baseClasses, \" \\n        \").concat(getBackgroundColor(title), \" \\n        text-white \\n        font-semibold \\n        \").concat(textSizes[size], \"\\n        shadow-sm\\n      \"),\n        style: style,\n        title: title,\n        children: size === 'full-height' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-24 h-24 text-white opacity-80\"\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: getInitials(title)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm opacity-90 break-words line-clamp-3\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 310,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            children: getInitials(title)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 318,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n        lineNumber: 297,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogAvatar, \"yA6MC4/13YXgE42AlKw5vrWMK58=\");\n_c = BlogAvatar;\nfunction BlogsManagement(param) {\n    let { config } = param;\n    var _config_defaultSort, _config_defaultSort1, _config_defaultViewSettings, _config_defaultViewSettings1, _config_defaultViewSettings2, _config_defaultViewSettings3, _config_defaultViewSettings4, _config_filters, _config_filters1, _config_permissions, _deleteConfirmation_bulkPosts, _deleteConfirmation_bulkPosts1, _deleteConfirmation_post;\n    _s1();\n    // Notification system\n    const { showSuccess, showError, showInfo, showLoading } = (0,_components_providers_notification_provider__WEBPACK_IMPORTED_MODULE_4__.useNotifications)();\n    // State management\n    const [blogPosts, setBlogPosts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [debouncedSearchQuery, setDebouncedSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultSort = config.defaultSort) === null || _config_defaultSort === void 0 ? void 0 : _config_defaultSort.field) || 'updatedAt');\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultSort1 = config.defaultSort) === null || _config_defaultSort1 === void 0 ? void 0 : _config_defaultSort1.direction) === 'asc' ? 'asc' : 'desc');\n    const [selectedPosts, setSelectedPosts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultViewSettings = config.defaultViewSettings) === null || _config_defaultViewSettings === void 0 ? void 0 : _config_defaultViewSettings.mode) === 'list' || ((_config_defaultViewSettings1 = config.defaultViewSettings) === null || _config_defaultViewSettings1 === void 0 ? void 0 : _config_defaultViewSettings1.mode) === 'grid' ? config.defaultViewSettings.mode : 'list');\n    const [density, setDensity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultViewSettings2 = config.defaultViewSettings) === null || _config_defaultViewSettings2 === void 0 ? void 0 : _config_defaultViewSettings2.density) === 'compact' || ((_config_defaultViewSettings3 = config.defaultViewSettings) === null || _config_defaultViewSettings3 === void 0 ? void 0 : _config_defaultViewSettings3.density) === 'comfortable' ? config.defaultViewSettings.density : 'comfortable');\n    const [activeActionMenu, setActiveActionMenu] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [visibleColumns, setVisibleColumns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(Array.isArray((_config_defaultViewSettings4 = config.defaultViewSettings) === null || _config_defaultViewSettings4 === void 0 ? void 0 : _config_defaultViewSettings4.visibleColumns) ? config.defaultViewSettings.visibleColumns : []);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showColumnSelector, setShowColumnSelector] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showWindowList, setShowWindowList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [gridColumns, setGridColumns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(3);\n    // Modal states\n    const [isCreateModalOpen, setIsCreateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isEditModalOpen, setIsEditModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [editingPost, setEditingPost] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Confirmation modal state\n    const [deleteConfirmation, setDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        isOpen: false,\n        post: null,\n        isBulkDelete: false,\n        bulkPosts: []\n    });\n    // Debounce search query\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BlogsManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"BlogsManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchQuery(searchQuery);\n                    setCurrentPage(1); // Reset to first page when searching\n                }\n            }[\"BlogsManagement.useEffect.timer\"], 300);\n            return ({\n                \"BlogsManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"BlogsManagement.useEffect\"];\n        }\n    }[\"BlogsManagement.useEffect\"], [\n        searchQuery\n    ]);\n    // Handle clicking outside to close action menu\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BlogsManagement.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"BlogsManagement.useEffect.handleClickOutside\": (event)=>{\n                    if (activeActionMenu) {\n                        const target = event.target;\n                        const actionMenu = document.querySelector('[data-post-id=\"'.concat(activeActionMenu, '\"] .action-menu'));\n                        const actionButton = document.querySelector('[data-post-id=\"'.concat(activeActionMenu, '\"] .action-button'));\n                        if (actionMenu && actionButton && !actionMenu.contains(target) && !actionButton.contains(target)) {\n                            setActiveActionMenu(null);\n                        }\n                    }\n                }\n            }[\"BlogsManagement.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"BlogsManagement.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"BlogsManagement.useEffect\"];\n        }\n    }[\"BlogsManagement.useEffect\"], [\n        activeActionMenu\n    ]);\n    // Fetch blog posts\n    const fetchBlogPosts = async function() {\n        let preserveFocus = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        setLoading(true);\n        if (!preserveFocus) {\n            showLoading('Loading Blog Posts', 'Retrieving blog posts...');\n        }\n        try {\n            const params = new URLSearchParams({\n                page: currentPage.toString(),\n                limit: (config.pageSize || 10).toString(),\n                search: debouncedSearchQuery,\n                sortBy,\n                sortOrder\n            });\n            // Add filters to params\n            Object.entries(filters).forEach((param)=>{\n                let [key, value] = param;\n                if (value) {\n                    params.append(key, value);\n                }\n            });\n            console.log('Fetching blog posts with params:', params.toString()); // Debug log\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"?\").concat(params));\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('API Error Response:', errorText);\n                throw new Error(\"HTTP error! status: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const data = await response.json();\n            console.log('Received blog posts data:', data); // Debug log\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to fetch blog posts');\n            }\n            setBlogPosts(data.posts || []);\n            setTotalPages(Math.ceil((data.totalCount || 0) / (config.pageSize || 10)));\n            setError(null); // Clear any previous errors on successful fetch\n            if (!preserveFocus) {\n                var _data_posts, _data_posts1;\n                showSuccess('Blog Posts Loaded', \"Loaded \".concat(((_data_posts = data.posts) === null || _data_posts === void 0 ? void 0 : _data_posts.length) || 0, \" blog post\").concat(((_data_posts1 = data.posts) === null || _data_posts1 === void 0 ? void 0 : _data_posts1.length) === 1 ? '' : 's'));\n            }\n        } catch (err) {\n            console.error('Error fetching blog posts:', err); // Debug log\n            setError(err instanceof Error ? err.message : 'Failed to fetch blog posts');\n            if (!preserveFocus) {\n                showError('Failed to Load Blog Posts', 'Unable to retrieve blog posts');\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BlogsManagement.useEffect\": ()=>{\n            // Preserve focus when searching\n            const isSearching = debouncedSearchQuery !== '';\n            fetchBlogPosts(isSearching);\n        }\n    }[\"BlogsManagement.useEffect\"], [\n        currentPage,\n        debouncedSearchQuery,\n        sortBy,\n        sortOrder,\n        filters\n    ]);\n    // Handle create\n    const handleCreate = async (formData)=>{\n        try {\n            showLoading('Creating Blog Post', 'Saving new blog post...');\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to create blog post (\".concat(response.status, \")\"));\n            }\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to create blog post');\n            }\n            setIsCreateModalOpen(false);\n            fetchBlogPosts();\n            showSuccess('Blog Post Created', '\"'.concat(formData.title || 'New blog post', '\" created successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to create blog post';\n            setError(errorMessage);\n            showError('Failed to Create Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Handle add button click\n    const handleAddClick = ()=>{\n        showInfo('Opening Create Form', 'Preparing to create a new blog post...');\n        setIsCreateModalOpen(true);\n    };\n    // Handle update\n    const handleUpdate = async (id, formData)=>{\n        try {\n            showLoading('Updating Blog Post', 'Saving changes...');\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to update blog post (\".concat(response.status, \")\"));\n            }\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to update blog post');\n            }\n            setIsEditModalOpen(false);\n            setEditingPost(null);\n            fetchBlogPosts();\n            showSuccess('Blog Post Updated', '\"'.concat(formData.title || 'Blog post', '\" updated successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to update blog post';\n            setError(errorMessage);\n            showError('Failed to Update Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Handle delete\n    const handleDelete = async (id)=>{\n        try {\n            const postToDelete = blogPosts.find((post)=>post.id === id);\n            showLoading('Deleting Blog Post', 'Removing \"'.concat((postToDelete === null || postToDelete === void 0 ? void 0 : postToDelete.title) || 'blog post', '\"...'));\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete blog post');\n            fetchBlogPosts();\n            showSuccess('Blog Post Deleted', '\"'.concat((postToDelete === null || postToDelete === void 0 ? void 0 : postToDelete.title) || 'Blog post', '\" deleted successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to delete blog post';\n            setError(errorMessage);\n            showError('Failed to Delete Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Handle duplicate\n    const handleDuplicate = async (post)=>{\n        try {\n            showLoading('Duplicating Blog Post', 'Creating copy of \"'.concat(post.title, '\"...'));\n            // Create duplicate data with modified title and slug\n            const duplicateData = {\n                title: \"\".concat(post.title, \" (Copy)\"),\n                slug: \"\".concat(post.slug, \"-copy-\").concat(Date.now()),\n                content: post.content,\n                excerpt: post.excerpt || '',\n                featuredImageUrl: post.featuredImageUrl || '',\n                authorId: post.authorId || '',\n                isPublished: false,\n                publishedAt: null,\n                categories: post.categories || '',\n                tags: post.tags || ''\n            };\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(duplicateData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to duplicate blog post (\".concat(response.status, \")\"));\n            }\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to duplicate blog post');\n            }\n            fetchBlogPosts();\n            showSuccess('Blog Post Duplicated', '\"'.concat(post.title, '\" duplicated successfully as \"').concat(duplicateData.title, '\"!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to duplicate blog post';\n            setError(errorMessage);\n            showError('Failed to Duplicate Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Show delete confirmation\n    const showDeleteConfirmation = (post)=>{\n        setDeleteConfirmation({\n            isOpen: true,\n            post,\n            isBulkDelete: false,\n            bulkPosts: []\n        });\n    };\n    // Show bulk delete confirmation\n    const showBulkDeleteConfirmation = ()=>{\n        const postsToDelete = blogPosts.filter((post)=>selectedPosts.includes(post.id));\n        setDeleteConfirmation({\n            isOpen: true,\n            post: null,\n            isBulkDelete: true,\n            bulkPosts: postsToDelete\n        });\n    };\n    // Confirm delete\n    const confirmDelete = async ()=>{\n        try {\n            if (deleteConfirmation.isBulkDelete && deleteConfirmation.bulkPosts) {\n                // Bulk delete\n                const promises = deleteConfirmation.bulkPosts.map((post)=>fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(post.id), {\n                        method: 'DELETE'\n                    }));\n                await Promise.all(promises);\n                setSelectedPosts([]);\n                fetchBlogPosts();\n                showSuccess('Blog Posts Deleted', \"\".concat(deleteConfirmation.bulkPosts.length, \" blog post(s) deleted successfully!\"));\n            } else if (deleteConfirmation.post) {\n                // Single delete\n                await handleDelete(deleteConfirmation.post.id);\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to delete blog post(s)';\n            showError('Failed to Delete Blog Post(s)', errorMessage);\n        } finally{\n            setDeleteConfirmation({\n                isOpen: false,\n                post: null,\n                isBulkDelete: false,\n                bulkPosts: []\n            });\n        }\n    };\n    // Cancel delete\n    const cancelDelete = ()=>{\n        setDeleteConfirmation({\n            isOpen: false,\n            post: null,\n            isBulkDelete: false,\n            bulkPosts: []\n        });\n    };\n    // Handle bulk actions\n    const handleBulkAction = async (action)=>{\n        if (selectedPosts.length === 0) return;\n        setActionLoading(action);\n        try {\n            if (action === 'delete') {\n                showBulkDeleteConfirmation();\n                return;\n            }\n            showLoading(\"Bulk \".concat(action), \"Processing \".concat(selectedPosts.length, \" blog post\").concat(selectedPosts.length === 1 ? '' : 's', \"...\"));\n            const promises = selectedPosts.map(async (id)=>{\n                switch(action){\n                    case 'publish':\n                        return fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                            method: 'PUT',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                isPublished: true\n                            })\n                        });\n                    case 'unpublish':\n                        return fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                            method: 'PUT',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                isPublished: false\n                            })\n                        });\n                    case 'duplicate':\n                        const postToDuplicate = blogPosts.find((post)=>post.id === id);\n                        if (postToDuplicate) {\n                            const duplicateData = {\n                                title: \"\".concat(postToDuplicate.title, \" (Copy)\"),\n                                slug: \"\".concat(postToDuplicate.slug, \"-copy-\").concat(Date.now()),\n                                content: postToDuplicate.content,\n                                excerpt: postToDuplicate.excerpt || '',\n                                featuredImageUrl: postToDuplicate.featuredImageUrl || '',\n                                authorId: postToDuplicate.authorId || '',\n                                isPublished: false,\n                                publishedAt: null,\n                                categories: postToDuplicate.categories || '',\n                                tags: postToDuplicate.tags || ''\n                            };\n                            return fetch(\"/api/admin/\".concat(config.endpoint), {\n                                method: 'POST',\n                                headers: {\n                                    'Content-Type': 'application/json'\n                                },\n                                body: JSON.stringify(duplicateData)\n                            });\n                        }\n                        throw new Error(\"Post not found for duplication: \".concat(id));\n                    default:\n                        throw new Error(\"Unknown bulk action: \".concat(action));\n                }\n            });\n            await Promise.all(promises);\n            setSelectedPosts([]);\n            fetchBlogPosts();\n            showSuccess(\"Bulk \".concat(action, \" completed\"), \"\".concat(selectedPosts.length, \" blog post\").concat(selectedPosts.length === 1 ? '' : 's', \" \").concat(action, \"ed successfully!\"));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"Failed to \".concat(action, \" blog posts\");\n            setError(errorMessage);\n            showError(\"Failed to \".concat(action, \" blog posts\"), errorMessage);\n        } finally{\n            setActionLoading(null);\n        }\n    };\n    // Handle individual actions\n    const handleAction = async (action, item)=>{\n        setActionLoading(\"\".concat(action, \"-\").concat(item.id));\n        try {\n            switch(action){\n                case 'edit':\n                    showInfo('Opening Editor', 'Editing \"'.concat(item.title, '\"'));\n                    setEditingPost(item);\n                    setIsEditModalOpen(true);\n                    break;\n                case 'view':\n                    showInfo('Opening View', 'Viewing \"'.concat(item.title, '\"'));\n                    // TODO: Implement view functionality\n                    showInfo('View Blog Post', 'Opening \"'.concat(item.title, '\" in new tab'));\n                    break;\n                case 'delete':\n                    showInfo('Delete Confirmation', 'Preparing to delete \"'.concat(item.title, '\"'));\n                    showDeleteConfirmation(item);\n                    break;\n                case 'toggle-published':\n                    const newStatus = !item.isPublished;\n                    showLoading(newStatus ? 'Publishing Blog Post' : 'Unpublishing Blog Post', \"\".concat(newStatus ? 'Publishing' : 'Unpublishing', ' \"').concat(item.title, '\"...'));\n                    await handleUpdate(item.id, {\n                        isPublished: newStatus\n                    });\n                    showSuccess(newStatus ? 'Blog Post Published' : 'Blog Post Unpublished', '\"'.concat(item.title, '\" ').concat(newStatus ? 'published' : 'unpublished', \" successfully!\"));\n                    break;\n                case 'duplicate':\n                    await handleDuplicate(item);\n                    break;\n                case 'archive':\n                    showLoading('Archiving Blog Post', 'Archiving \"'.concat(item.title, '\"...'));\n                    // TODO: Implement archive functionality\n                    showSuccess('Blog Post Archived', '\"'.concat(item.title, '\" archived successfully!'));\n                    break;\n                default:\n                    console.warn(\"Unknown action: \".concat(action));\n            }\n        } finally{\n            setActionLoading(null);\n        }\n    };\n    // Handle sorting\n    const handleSort = (field)=>{\n        const newOrder = sortBy === field ? sortOrder === 'asc' ? 'desc' : 'asc' : 'asc';\n        const newField = sortBy === field ? field : field;\n        setSortBy(newField);\n        setSortOrder(newOrder);\n        setCurrentPage(1);\n        showInfo('Sorting Blog Posts', \"Sorting by \".concat(field, \" (\").concat(newOrder, \")\"));\n    };\n    // Handle selection\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedPosts(blogPosts.map((post)=>post.id));\n            showInfo('Selection Updated', \"Selected all \".concat(blogPosts.length, \" blog post\").concat(blogPosts.length === 1 ? '' : 's'));\n        } else {\n            setSelectedPosts([]);\n            showInfo('Selection Cleared', 'Deselected all blog posts');\n        }\n    };\n    const handleSelectPost = (id, checked)=>{\n        if (checked) {\n            setSelectedPosts([\n                ...selectedPosts,\n                id\n            ]);\n            const post = blogPosts.find((p)=>p.id === id);\n            showInfo('Post Selected', '\"'.concat((post === null || post === void 0 ? void 0 : post.title) || 'Blog post', '\" added to selection'));\n        } else {\n            setSelectedPosts(selectedPosts.filter((postId)=>postId !== id));\n            const post = blogPosts.find((p)=>p.id === id);\n            showInfo('Post Deselected', '\"'.concat((post === null || post === void 0 ? void 0 : post.title) || 'Blog post', '\" removed from selection'));\n        }\n    };\n    // Get visible fields for table\n    const getVisibleFields = ()=>{\n        if (visibleColumns.length > 0) {\n            var _config_fields;\n            return ((_config_fields = config.fields) === null || _config_fields === void 0 ? void 0 : _config_fields.filter((field)=>visibleColumns.includes(field.key))) || [];\n        }\n        return config.fields || [];\n    };\n    // Handle view mode change\n    const handleViewModeChange = (mode)=>{\n        setViewMode(mode);\n        showInfo('View Mode Changed', \"Switched to \".concat(mode, \" view\"));\n    };\n    // Handle density change\n    const handleDensityChange = (newDensity)=>{\n        setDensity(newDensity);\n        showInfo('Density Updated', \"Changed to \".concat(newDensity, \" density\"));\n    };\n    // Handle grid columns change\n    const handleGridColumnsChange = (columns)=>{\n        setGridColumns(columns);\n        showInfo('Grid Layout Updated', \"Changed to \".concat(columns, \" column\").concat(columns === 1 ? '' : 's', \" layout\"));\n    };\n    // Handle filter changes\n    const handleFilterChange = (key, value)=>{\n        const newFilters = {\n            ...filters\n        };\n        if (value) {\n            newFilters[key] = value;\n        } else {\n            delete newFilters[key];\n        }\n        setFilters(newFilters);\n        showInfo('Filter Applied', \"Filter updated: \".concat(key, \" = \").concat(value || 'all'));\n    };\n    // Close all action menus\n    const closeAllActionMenus = ()=>{\n        setActiveActionMenu(null);\n    };\n    // Grid Card Component\n    const GridCard = (param)=>{\n        let { post } = param;\n        const isSelected = selectedPosts.includes(post.id);\n        const classes = getGridDensityClasses();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            \"data-post-id\": post.id,\n            className: \"group relative bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 \".concat(isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : '', \" \").concat(classes.card),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"aspect-video bg-gray-100 rounded-t-lg overflow-hidden\",\n                    children: post.featuredImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: post.featuredImageUrl,\n                        alt: post.title,\n                        className: \"w-full h-full object-cover\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 891,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-12 h-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 898,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 897,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 889,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: classes.content,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"\".concat(classes.title, \" text-gray-900 line-clamp-2\"),\n                            children: post.title\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 906,\n                            columnNumber: 11\n                        }, this),\n                        post.excerpt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"\".concat(classes.excerpt, \" text-gray-600 line-clamp-3\"),\n                            children: post.excerpt\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 912,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: classes.meta,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex \".concat(classes.status, \" font-semibold rounded-full \").concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                            children: post.isPublished ? 'Published' : 'Draft'\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 921,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"\".concat(classes.date, \" text-gray-500\"),\n                                            children: formatDate(post.updatedAt)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 928,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 920,\n                                    columnNumber: 17\n                                }, this),\n                                post.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap \".concat(classes.categories),\n                                    children: post.categories.split(',').slice(0, 2).map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex \".concat(classes.category, \" bg-blue-100 text-blue-800 rounded\"),\n                                            children: category.trim()\n                                        }, index, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 937,\n                                            columnNumber: 23\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 935,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 918,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between \".concat(density === 'compact' ? 'mt-3 pt-2' : 'mt-4 pt-3', \" border-t border-gray-200\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: [\n                                        \"Updated \",\n                                        formatDate(post.updatedAt)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 947,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"action-button p-2 text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors\",\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            const isVisible = activeActionMenu === post.id;\n                                            if (isVisible) {\n                                                // Hide menu\n                                                setActiveActionMenu(null);\n                                            } else {\n                                                // Show menu (this will automatically hide any other open menu)\n                                                setActiveActionMenu(post.id);\n                                            }\n                                        },\n                                        title: \"Show Actions\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 968,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 953,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 952,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 946,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"action-menu absolute bg-white rounded-lg border border-gray-200 shadow-lg flex flex-col items-center justify-center transition-all duration-200 z-50 \".concat(density === 'compact' ? 'top-1 right-1 bottom-1 w-12 space-y-1.5 px-1' : 'top-2 right-2 bottom-2 w-14 space-y-2 px-1.5'),\n                            style: {\n                                opacity: activeActionMenu === post.id ? '1' : '0',\n                                transform: activeActionMenu === post.id ? 'translateX(0)' : 'translateX(100%)',\n                                pointerEvents: activeActionMenu === post.id ? 'auto' : 'none'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeAllActionMenus();\n                                        handleAction('edit', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 border border-blue-500 hover:border-blue-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : 'w-10 h-10'),\n                                    title: \"Edit Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 995,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 984,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeAllActionMenus();\n                                        handleAction('view', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-indigo-600 hover:bg-indigo-700 border border-indigo-500 hover:border-indigo-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : 'w-10 h-10'),\n                                    title: \"View Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1010,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 999,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeAllActionMenus();\n                                        handleAction('toggle-published', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center \".concat(post.isPublished ? 'bg-orange-600 hover:bg-orange-700 border-orange-500 hover:border-orange-600' : 'bg-green-600 hover:bg-green-700 border-green-500 hover:border-green-600', \" border text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \").concat(density === 'compact' ? 'w-8 h-8' : 'w-10 h-10'),\n                                    title: post.isPublished ? 'Unpublish Blog Post' : 'Publish Blog Post',\n                                    children: post.isPublished ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1030,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1032,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1014,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeAllActionMenus();\n                                        handleAction('duplicate', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-purple-600 hover:bg-purple-700 border border-purple-500 hover:border-purple-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : 'w-10 h-10'),\n                                    title: \"Duplicate Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1048,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1037,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeAllActionMenus();\n                                        handleAction('archive', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-yellow-600 hover:bg-yellow-700 border border-yellow-500 hover:border-yellow-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : 'w-10 h-10'),\n                                    title: \"Archive Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1063,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1052,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeAllActionMenus();\n                                        handleAction('delete', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-red-600 hover:bg-red-700 border border-red-500 hover:border-red-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : 'w-10 h-10'),\n                                    title: \"Delete Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1078,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1067,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 974,\n                            columnNumber: 11\n                        }, this),\n                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 left-2 \".concat(classes.checkbox),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                checked: isSelected,\n                                onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white \".concat(classes.checkbox),\n                                style: {\n                                    backgroundColor: 'white'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1086,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1085,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 904,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 882,\n            columnNumber: 7\n        }, this);\n    };\n    // Grid density styling helper\n    const getGridDensityClasses = ()=>{\n        const baseClasses = {\n            container: {\n                compact: 'p-1',\n                comfortable: 'p-3'\n            },\n            grid: {\n                compact: 'gap-2',\n                comfortable: 'gap-3'\n            },\n            card: {\n                compact: 'p-1',\n                comfortable: 'p-2'\n            },\n            content: {\n                compact: 'p-1',\n                comfortable: 'p-2'\n            },\n            title: {\n                compact: 'text-sm font-semibold mb-0.5',\n                comfortable: 'text-lg font-semibold mb-1'\n            },\n            excerpt: {\n                compact: 'text-xs mb-1',\n                comfortable: 'text-sm mb-1.5'\n            },\n            meta: {\n                compact: 'space-y-0.5 mb-1',\n                comfortable: 'space-y-1 mb-2'\n            },\n            status: {\n                compact: 'px-1 py-0.5 text-xs',\n                comfortable: 'px-1.5 py-0.5 text-xs'\n            },\n            date: {\n                compact: 'text-xs',\n                comfortable: 'text-xs'\n            },\n            categories: {\n                compact: 'gap-0.5',\n                comfortable: 'gap-0.5'\n            },\n            category: {\n                compact: 'px-1 py-0.5 text-xs',\n                comfortable: 'px-1.5 py-0.5 text-xs'\n            },\n            actions: {\n                compact: 'pt-1',\n                comfortable: 'pt-1.5'\n            },\n            buttons: {\n                compact: 'space-x-0.5',\n                comfortable: 'space-x-1'\n            },\n            button: {\n                compact: 'p-0.5',\n                comfortable: 'p-1'\n            },\n            icon: {\n                compact: 'w-2.5 h-2.5',\n                comfortable: 'w-3 h-3'\n            },\n            checkbox: {\n                compact: 'h-2.5 w-2.5',\n                comfortable: 'h-3 w-3'\n            }\n        };\n        return {\n            container: baseClasses.container[density],\n            grid: baseClasses.grid[density],\n            card: baseClasses.card[density],\n            content: baseClasses.content[density],\n            title: baseClasses.title[density],\n            excerpt: baseClasses.excerpt[density],\n            meta: baseClasses.meta[density],\n            status: baseClasses.status[density],\n            date: baseClasses.date[density],\n            categories: baseClasses.categories[density],\n            category: baseClasses.category[density],\n            actions: baseClasses.actions[density],\n            buttons: baseClasses.buttons[density],\n            button: baseClasses.button[density],\n            icon: baseClasses.icon[density],\n            checkbox: baseClasses.checkbox[density]\n        };\n    };\n    // Format date\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString();\n    };\n    // Truncate text\n    const truncateText = (text, maxLength)=>{\n        if (text.length <= maxLength) return text;\n        return text.substring(0, maxLength) + '...';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"h-full flex flex-col space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"d5ed160e6f329c5c\",\n                children: \".action-menu{transition:all.2s ease-in-out}@media(min-width:1025px){.group:hover .action-menu{opacity:1!important;transform:translatex(0)!important;pointer-events:auto!important}}.density-compact th,.density-compact td{padding-top:4px!important;padding-bottom:4px!important}.density-comfortable th,.density-comfortable td{padding-top:16px!important;padding-bottom:16px!important}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative rounded-lg shadow-sm border border-gray-200/50 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-indigo-50/20\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 1228,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative p-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-14 w-14 text-lime-600 -mt-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1233,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-3xl font-bold text-gray-900 mt-2\",\n                                                    children: \"Blog Management\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1235,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm font-medium text-gray-600\",\n                                                    children: \"Create, edit, and manage your blog content.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1238,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1234,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1232,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"hidden lg:flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAddClick,\n                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors shadow-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1249,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Add Blog Post\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1245,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1244,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1231,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 1230,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 1227,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                role: \"main\",\n                \"aria-label\": \"Blog management section\",\n                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex-1 rounded-lg shadow-sm border border-gray-200/50 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        paddingBottom: '0'\n                    },\n                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"space-y-3 p-2 bg-white rounded-lg border border-gray-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex flex-col space-y-3 lg:hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1272,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"Search blog posts...\",\n                                                        value: searchQuery,\n                                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"pl-10 pr-4 py-2.5 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1273,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1271,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1270,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-0.5 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleViewModeChange('list'),\n                                                            title: \"List view\",\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 \".concat(viewMode === 'list' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1296,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-xs font-medium hidden xs:inline\",\n                                                                    children: \"List\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1297,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1287,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleViewModeChange('grid'),\n                                                            title: \"Grid view\",\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 \".concat(viewMode === 'grid' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1308,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-xs font-medium hidden xs:inline\",\n                                                                    children: \"Grid\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1309,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1299,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1286,\n                                                    columnNumber: 15\n                                                }, this),\n                                                viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-0.5 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-xs font-medium text-gray-700 px-1\",\n                                                            children: \"Col:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1316,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center gap-0.5 flex-1\",\n                                                            children: [\n                                                                1,\n                                                                2,\n                                                                3,\n                                                                4\n                                                            ].map((num)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(num),\n                                                                    title: \"\".concat(num, \" column\").concat(num > 1 ? 's' : ''),\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex-1 px-1.5 py-1 rounded text-xs font-medium \".concat(gridColumns === num ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: num\n                                                                }, num, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1319,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1317,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1315,\n                                                    columnNumber: 17\n                                                }, this),\n                                                viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowColumnSelector(!showColumnSelector),\n                                                        title: \"Columns\",\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1344,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"hidden xs:inline\",\n                                                                children: \"Col\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1345,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-3 w-3 ml-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1346,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1339,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1338,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowFilters(!showFilters),\n                                                        title: \"Filters\",\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium rounded-lg border \".concat(showFilters || Object.keys(filters).some((key)=>filters[key]) ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1362,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"hidden xs:inline\",\n                                                                children: \"Filter\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1363,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            Object.keys(filters).some((key)=>filters[key]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"ml-0.5 inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                children: Object.values(filters).filter(Boolean).length\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1365,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1353,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1352,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowWindowList(!showWindowList),\n                                                        title: \"Density\",\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1379,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"hidden xs:inline\",\n                                                                children: density.charAt(0).toUpperCase() + density.slice(1)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1380,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-3 w-3 ml-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1381,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1374,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1373,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1284,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1268,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"hidden lg:flex items-center justify-between gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center gap-3 flex-1 max-w-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1393,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"Search blog posts by title, content, excerpt...\",\n                                                            value: searchQuery,\n                                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1394,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1392,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowFilters(!showFilters),\n                                                            title: \"Show/hide filters\",\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(showFilters || Object.keys(filters).some((key)=>filters[key]) ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1414,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Filters\",\n                                                                Object.keys(filters).some((key)=>filters[key]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                    children: Object.values(filters).filter(Boolean).length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1417,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1421,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1405,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"absolute top-full right-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center justify-between mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                                                children: \"Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1429,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>setShowFilters(false),\n                                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-gray-400 hover:text-gray-600\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1434,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1430,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1428,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"space-y-4\",\n                                                                        children: (_config_filters = config.filters) === null || _config_filters === void 0 ? void 0 : _config_filters.map((filter)=>{\n                                                                            var _filter_options;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-d5ed160e6f329c5c\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"block text-xs font-medium text-gray-700 mb-2\",\n                                                                                        children: filter.label\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1441,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                        value: filters[filter.key] || '',\n                                                                                        onChange: (e)=>{\n                                                                                            const newFilters = {\n                                                                                                ...filters\n                                                                                            };\n                                                                                            if (e.target.value) {\n                                                                                                newFilters[filter.key] = e.target.value;\n                                                                                            } else {\n                                                                                                delete newFilters[filter.key];\n                                                                                            }\n                                                                                            setFilters(newFilters);\n                                                                                        },\n                                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                                                        children: (_filter_options = filter.options) === null || _filter_options === void 0 ? void 0 : _filter_options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: option.value,\n                                                                                                className: \"jsx-d5ed160e6f329c5c\",\n                                                                                                children: option.label\n                                                                                            }, option.value, false, {\n                                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                                lineNumber: 1458,\n                                                                                                columnNumber: 37\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1444,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, filter.key, true, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1440,\n                                                                                columnNumber: 31\n                                                                            }, this);\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1438,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex justify-end mt-4 pt-4 border-t border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                setFilters({});\n                                                                                setShowFilters(false);\n                                                                            },\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg\",\n                                                                            children: \"Clear All\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1468,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1467,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1427,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1426,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1404,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1391,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm font-medium text-gray-700\",\n                                                            children: \"View:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1488,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-1 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleViewModeChange('list'),\n                                                                    title: \"List view\",\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-3 py-2 rounded-md flex items-center gap-2 \".concat(viewMode === 'list' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-5 w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1499,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm font-medium\",\n                                                                            children: \"List\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1500,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1490,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleViewModeChange('grid'),\n                                                                    title: \"Grid view\",\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-3 py-2 rounded-md flex items-center gap-2 \".concat(viewMode === 'grid' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-5 w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1511,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm font-medium\",\n                                                                            children: \"Grid\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1512,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1502,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1489,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1487,\n                                                    columnNumber: 19\n                                                }, this),\n                                                viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm font-medium text-gray-700\",\n                                                            children: \"Columns:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1520,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-1 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(1),\n                                                                    title: \"1 column\",\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 1 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1522,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(2),\n                                                                    title: \"2 columns\",\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 2 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1533,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(3),\n                                                                    title: \"3 columns\",\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 3 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1544,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(4),\n                                                                    title: \"4 columns\",\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 4 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1555,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1521,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1519,\n                                                    columnNumber: 21\n                                                }, this),\n                                                viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowColumnSelector(!showColumnSelector),\n                                                            title: \"Select columns to display\",\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1578,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Columns\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1580,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1573,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        showColumnSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-xs font-medium text-gray-500 uppercase tracking-wide mb-2\",\n                                                                        children: \"Show Columns\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1587,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    config.columns.map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-2 py-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: visibleColumns.includes(column.key),\n                                                                                    onChange: (e)=>{\n                                                                                        if (e.target.checked) {\n                                                                                            setVisibleColumns((prev)=>[\n                                                                                                    ...prev,\n                                                                                                    column.key\n                                                                                                ]);\n                                                                                        } else {\n                                                                                            setVisibleColumns((prev)=>prev.filter((col)=>col !== column.key));\n                                                                                        }\n                                                                                    },\n                                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1590,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm text-gray-700\",\n                                                                                    children: column.label\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1602,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, column.key, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1589,\n                                                                            columnNumber: 31\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1586,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1585,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1572,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowWindowList(!showWindowList),\n                                                            title: \"Select density\",\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1620,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                density.charAt(0).toUpperCase() + density.slice(1),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1622,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1615,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showWindowList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"absolute top-full left-0 mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"p-1\",\n                                                                children: [\n                                                                    'compact',\n                                                                    'comfortable'\n                                                                ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            handleDensityChange(option);\n                                                                            setShowWindowList(false);\n                                                                        },\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 \".concat(density === option ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'),\n                                                                        children: option.charAt(0).toUpperCase() + option.slice(1)\n                                                                    }, option, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1630,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1628,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1627,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1614,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1485,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1389,\n                                    columnNumber: 15\n                                }, this),\n                                showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                    children: \"Filters\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1656,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowFilters(false),\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-gray-400 hover:text-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1661,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1657,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1655,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"space-y-4\",\n                                            children: (_config_filters1 = config.filters) === null || _config_filters1 === void 0 ? void 0 : _config_filters1.map((filter)=>{\n                                                var _filter_options;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"block text-xs font-medium text-gray-700 mb-2\",\n                                                            children: filter.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1668,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: filters[filter.key] || '',\n                                                            onChange: (e)=>{\n                                                                const newFilters = {\n                                                                    ...filters\n                                                                };\n                                                                if (e.target.value) {\n                                                                    newFilters[filter.key] = e.target.value;\n                                                                } else {\n                                                                    delete newFilters[filter.key];\n                                                                }\n                                                                setFilters(newFilters);\n                                                            },\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                            children: (_filter_options = filter.options) === null || _filter_options === void 0 ? void 0 : _filter_options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: option.value,\n                                                                    className: \"jsx-d5ed160e6f329c5c\",\n                                                                    children: option.label\n                                                                }, option.value, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1685,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1671,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, filter.key, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1667,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1665,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex justify-end mt-4 pt-4 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setFilters({});\n                                                    setShowFilters(false);\n                                                },\n                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg\",\n                                                children: \"Clear All\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1695,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1694,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1654,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1266,\n                            columnNumber: 11\n                        }, this),\n                        config.enableBulkActions && selectedPosts.length > 0 && (viewMode === 'list' || viewMode === 'grid') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"bg-blue-50 border border-blue-200 rounded-lg px-4 py-2 shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-blue-600 font-semibold text-xs\",\n                                                            children: selectedPosts.length\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1716,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1715,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-xs font-medium text-blue-900\",\n                                                        children: [\n                                                            \"blog post\",\n                                                            selectedPosts.length === 1 ? '' : 's',\n                                                            \" selected\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1720,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1714,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleBulkAction('publish'),\n                                                        title: \"Publish selected blog posts\",\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 border border-green-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1732,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Publish\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1727,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleBulkAction('unpublish'),\n                                                        title: \"Unpublish selected blog posts\",\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1741,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Unpublish\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1736,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>showBulkDeleteConfirmation(),\n                                                        title: \"Delete selected blog posts\",\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 border border-red-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1750,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Delete\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1745,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1726,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1713,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-1.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedPosts([]),\n                                            title: \"Clear selection\",\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gray-500 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1763,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Clear\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1758,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1757,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1712,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1711,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-5 w-5 text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1777,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1776,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm font-medium text-red-800\",\n                                                    children: \"Error\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1780,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"mt-2 text-sm text-red-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-d5ed160e6f329c5c\",\n                                                        children: error\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1782,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1781,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setError(null);\n                                                            fetchBlogPosts();\n                                                        },\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200\",\n                                                        children: \"Try Again\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1785,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1784,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1779,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1775,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1774,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1773,\n                            columnNumber: 15\n                        }, this),\n                        loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-6 py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1805,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"ml-3 text-gray-600\",\n                                        children: \"Loading blog posts...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1806,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1804,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1803,\n                            columnNumber: 15\n                        }, this),\n                        !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-d5ed160e6f329c5c\",\n                            children: blogPosts.length === 0 ? /* Empty State */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"p-12 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"mx-auto h-12 w-12 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1817,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"mt-2 text-sm font-medium text-gray-900\",\n                                        children: \"No blog posts found\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1818,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"mt-1 text-sm text-gray-500\",\n                                        children: debouncedSearchQuery ? 'Try adjusting your search terms.' : 'Get started by creating your first blog post.'\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1819,\n                                        columnNumber: 21\n                                    }, this),\n                                    ((_config_permissions = config.permissions) === null || _config_permissions === void 0 ? void 0 : _config_permissions.create) && !debouncedSearchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleAddClick,\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: \"Add Blog Post\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1824,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1823,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1816,\n                                columnNumber: 19\n                            }, this) : viewMode === 'list' ? /* Table View */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"min-w-full divide-y divide-gray-200 density-\".concat(density),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"bg-gray-200 border-b border-gray-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"jsx-d5ed160e6f329c5c\",\n                                                    children: [\n                                                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            scope: \"col\",\n                                                            style: {\n                                                                width: '6px'\n                                                            },\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"relative pl-2 py-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: selectedPosts.length === blogPosts.length && blogPosts.length > 0,\n                                                                onChange: (e)=>handleSelectAll(e.target.checked),\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-blue-600 focus:ring-blue-500 border-gray-300 rounded \".concat(density === 'compact' ? 'h-4 w-4' : 'h-4 w-4')\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1843,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1842,\n                                                            columnNumber: 31\n                                                        }, this),\n                                                        getVisibleFields().map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                scope: \"col\",\n                                                                onClick: ()=>handleSort(field.key),\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-6 py-2 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 text-xs\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-d5ed160e6f329c5c\",\n                                                                            children: field.label\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1863,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        sortBy === field.key ? sortOrder === 'asc' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-black\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1866,\n                                                                            columnNumber: 39\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-black\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1868,\n                                                                            columnNumber: 39\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1871,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1862,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, field.key, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1856,\n                                                                columnNumber: 31\n                                                            }, this)),\n                                                        config.actions && config.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            scope: \"col\",\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-6 py-2 text-right font-medium text-gray-900 uppercase tracking-wider text-xs min-w-[120px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\",\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1880,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1879,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1839,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1838,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"bg-white divide-y divide-gray-200\",\n                                                children: blogPosts.map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"hover:bg-gray-50 \".concat(selectedPosts.includes(post.id) ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''),\n                                                        children: [\n                                                            config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: selectedPosts.includes(post.id),\n                                                                    onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                                                    style: {\n                                                                        backgroundColor: 'white'\n                                                                    },\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1893,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1892,\n                                                                columnNumber: 33\n                                                            }, this),\n                                                            getVisibleFields().map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-6 whitespace-nowrap\",\n                                                                    children: field.key === 'title' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BlogAvatar, {\n                                                                                title: post.title,\n                                                                                featuredImageUrl: post.featuredImageUrl,\n                                                                                size: \"sm\",\n                                                                                className: \"mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1908,\n                                                                                columnNumber: 39\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-d5ed160e6f329c5c\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                                                        children: post.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1915,\n                                                                                        columnNumber: 41\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm text-gray-500\",\n                                                                                        children: post.slug\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1916,\n                                                                                        columnNumber: 41\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1914,\n                                                                                columnNumber: 39\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1907,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'isPublished' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                        children: post.isPublished ? 'Published' : 'Draft'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1920,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'excerpt' || field.key === 'content' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        title: post[field.key],\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm text-gray-900 max-w-xs truncate\",\n                                                                        children: truncateText(post[field.key] || '', 50)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1928,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'tags' || field.key === 'categories' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm text-gray-900\",\n                                                                        children: post[field.key] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex flex-wrap gap-1\",\n                                                                            children: [\n                                                                                post[field.key].split(',').slice(0, 2).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\",\n                                                                                        children: tag.trim()\n                                                                                    }, index, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1936,\n                                                                                        columnNumber: 45\n                                                                                    }, this)),\n                                                                                post[field.key].split(',').length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-xs text-gray-500\",\n                                                                                    children: [\n                                                                                        \"+\",\n                                                                                        post[field.key].split(',').length - 2,\n                                                                                        \" more\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1941,\n                                                                                    columnNumber: 45\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1934,\n                                                                            columnNumber: 41\n                                                                        }, this) : '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1932,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'createdAt' || field.key === 'updatedAt' || field.key === 'publishedAt' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm text-gray-500\",\n                                                                        children: post[field.key] ? formatDate(post[field.key]) : '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1947,\n                                                                        columnNumber: 37\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm text-gray-900\",\n                                                                        children: post[field.key] || '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1951,\n                                                                        columnNumber: 37\n                                                                    }, this)\n                                                                }, field.key, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1905,\n                                                                    columnNumber: 33\n                                                                }, this)),\n                                                            config.actions && config.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"px-6 whitespace-nowrap text-right text-sm font-medium\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-2\",\n                                                                    children: config.actions.map((action)=>{\n                                                                        const isLoading = actionLoading === \"\".concat(action.action, \"-\").concat(post.id);\n                                                                        const IconComponent = action.icon === 'EyeIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"] : action.icon === 'PencilIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"] : action.icon === 'PowerIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"] : action.icon === 'TrashIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"] : _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleAction(action.action, post),\n                                                                            disabled: isLoading,\n                                                                            title: action.tooltip,\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"p-1 rounded-md transition-colors \".concat(action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' : action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' : action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' : action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' : 'text-gray-600 hover:bg-gray-50', \" \").concat(isLoading ? 'opacity-50 cursor-not-allowed' : ''),\n                                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"animate-spin rounded-full h-4 w-4 border-b-2 border-current\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1984,\n                                                                                columnNumber: 45\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1986,\n                                                                                columnNumber: 45\n                                                                            }, this)\n                                                                        }, action.action, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1970,\n                                                                            columnNumber: 41\n                                                                        }, this);\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1961,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1960,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        ]\n                                                    }, post.id, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1887,\n                                                        columnNumber: 29\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1885,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1837,\n                                        columnNumber: 23\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1836,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1835,\n                                columnNumber: 19\n                            }, this) : viewMode === 'grid' ? /* Grid View - Restructured */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + (getGridDensityClasses().container || \"\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"grid \".concat(getGridDensityClasses().grid, \" \").concat(gridColumns === 1 ? 'grid-cols-1' : gridColumns === 2 ? 'grid-cols-1 md:grid-cols-2' : gridColumns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'),\n                                    children: blogPosts.map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridCard, {\n                                            post: post\n                                        }, post.id, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 2010,\n                                            columnNumber: 25\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 2003,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 2002,\n                                columnNumber: 19\n                            }, this) : /* Card View */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"space-y-4\",\n                                    children: blogPosts.map((post)=>{\n                                        var _config_actions;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow \".concat(selectedPosts.includes(post.id) ? 'ring-2 ring-blue-500 bg-blue-50' : ''),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-48 h-32 bg-gray-100 rounded-l-lg overflow-hidden flex-shrink-0\",\n                                                        children: post.featuredImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: post.featuredImageUrl,\n                                                            alt: post.title,\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-full h-full object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 2026,\n                                                            columnNumber: 33\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-full h-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-8 h-8 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 2033,\n                                                                columnNumber: 35\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 2032,\n                                                            columnNumber: 33\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 2024,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex-1 p-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-start justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-lg font-semibold text-gray-900 mb-1\",\n                                                                            children: post.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2043,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm text-gray-500 mb-2\",\n                                                                            children: post.slug\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2046,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        post.excerpt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"text-sm text-gray-600 mb-3 line-clamp-2\",\n                                                                            children: post.excerpt\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2052,\n                                                                            columnNumber: 37\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-4 text-sm text-gray-500\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                                    children: post.isPublished ? 'Published' : 'Draft'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2059,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-d5ed160e6f329c5c\",\n                                                                                    children: [\n                                                                                        \"Updated: \",\n                                                                                        formatDate(post.updatedAt)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2066,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                post.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-d5ed160e6f329c5c\",\n                                                                                    children: [\n                                                                                        \"Category: \",\n                                                                                        post.categories.split(',')[0].trim()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2068,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2058,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 2041,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"flex items-center space-x-2 ml-4\",\n                                                                    children: [\n                                                                        (_config_actions = config.actions) === null || _config_actions === void 0 ? void 0 : _config_actions.map((action)=>{\n                                                                            const isLoading = actionLoading === \"\".concat(action.action, \"-\").concat(post.id);\n                                                                            const IconComponent = action.icon === 'EyeIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"] : action.icon === 'PencilIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"] : action.icon === 'PowerIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"] : action.icon === 'TrashIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"] : _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleAction(action.action, post),\n                                                                                disabled: isLoading,\n                                                                                title: action.tooltip,\n                                                                                className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"p-2 rounded-md transition-colors \".concat(action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' : action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' : action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' : action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' : 'text-gray-600 hover:bg-gray-50', \" \").concat(isLoading ? 'opacity-50 cursor-not-allowed' : ''),\n                                                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"animate-spin rounded-full h-4 w-4 border-b-2 border-current\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2097,\n                                                                                    columnNumber: 43\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                    className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2099,\n                                                                                    columnNumber: 43\n                                                                                }, this)\n                                                                            }, action.action, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 2083,\n                                                                                columnNumber: 39\n                                                                            }, this);\n                                                                        }),\n                                                                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: selectedPosts.includes(post.id),\n                                                                            onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                                                            style: {\n                                                                                backgroundColor: 'white'\n                                                                            },\n                                                                            className: \"jsx-d5ed160e6f329c5c\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2107,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 2074,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 2040,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 2039,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 2022,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, post.id, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 2019,\n                                            columnNumber: 25\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 2017,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 2016,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1813,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 1264,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 1259,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_modal__WEBPACK_IMPORTED_MODULE_3__.BlogModal, {\n                isOpen: isCreateModalOpen,\n                onClose: ()=>setIsCreateModalOpen(false),\n                onSubmit: handleCreate,\n                title: \"Create New Blog Post\"\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 2130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_modal__WEBPACK_IMPORTED_MODULE_3__.BlogModal, {\n                isOpen: isEditModalOpen,\n                onClose: ()=>{\n                    setIsEditModalOpen(false);\n                    setEditingPost(null);\n                },\n                onSubmit: async (formData)=>{\n                    if (editingPost) {\n                        await handleUpdate(editingPost.id, formData);\n                    }\n                },\n                title: \"Edit Blog Post\",\n                initialData: editingPost !== null && editingPost !== void 0 ? editingPost : undefined\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 2137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_confirmation_modal__WEBPACK_IMPORTED_MODULE_5__.ConfirmationModal, {\n                isOpen: deleteConfirmation.isOpen,\n                title: \"Delete Confirmation\",\n                message: \"Are you sure you want to delete this blog post?\",\n                details: (()=>{\n                    if (deleteConfirmation.isBulkDelete) {\n                        var _deleteConfirmation_bulkPosts;\n                        const count = ((_deleteConfirmation_bulkPosts = deleteConfirmation.bulkPosts) === null || _deleteConfirmation_bulkPosts === void 0 ? void 0 : _deleteConfirmation_bulkPosts.length) || 0;\n                        return \"This action will permanently delete \".concat(count, \" blog post\").concat(count === 1 ? '' : 's', \". This cannot be undone.\");\n                    }\n                    const post = deleteConfirmation.post;\n                    return 'This action will permanently delete \"'.concat((post === null || post === void 0 ? void 0 : post.title) || 'this blog post', '\". This cannot be undone.');\n                })(),\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                onConfirm: confirmDelete,\n                onCancel: cancelDelete,\n                type: \"danger\",\n                showVerification: true,\n                verificationData: {\n                    canDelete: true,\n                    reason: deleteConfirmation.isBulkDelete ? \"\".concat(((_deleteConfirmation_bulkPosts = deleteConfirmation.bulkPosts) === null || _deleteConfirmation_bulkPosts === void 0 ? void 0 : _deleteConfirmation_bulkPosts.length) || 0, \" blog post\").concat((((_deleteConfirmation_bulkPosts1 = deleteConfirmation.bulkPosts) === null || _deleteConfirmation_bulkPosts1 === void 0 ? void 0 : _deleteConfirmation_bulkPosts1.length) || 0) === 1 ? '' : 's', \" selected for deletion\") : 'Blog post \"'.concat(((_deleteConfirmation_post = deleteConfirmation.post) === null || _deleteConfirmation_post === void 0 ? void 0 : _deleteConfirmation_post.title) || 'Unknown', '\" ready for deletion')\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 2153,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n        lineNumber: 1201,\n        columnNumber: 5\n    }, this);\n}\n_s1(BlogsManagement, \"ggm4gqsBMvPb+FXTAqxKPBO0Zns=\", false, function() {\n    return [\n        _components_providers_notification_provider__WEBPACK_IMPORTED_MODULE_4__.useNotifications\n    ];\n});\n_c1 = BlogsManagement;\nvar _c, _c1;\n$RefreshReg$(_c, \"BlogAvatar\");\n$RefreshReg$(_c1, \"BlogsManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/blog/blogs-management.tsx\n"));

/***/ })

});