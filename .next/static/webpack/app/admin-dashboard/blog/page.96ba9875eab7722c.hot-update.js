"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin-dashboard/blog/page",{

/***/ "(app-pages-browser)/./src/components/admin/blog/blogs-management.tsx":
/*!********************************************************!*\
  !*** ./src/components/admin/blog/blogs-management.tsx ***!
  \********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BlogsManagement: () => (/* binding */ BlogsManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EllipsisVerticalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/EyeSlashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PowerIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/DocumentDuplicateIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArchiveBoxIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/TrashIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PlusIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ListBulletIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/Squares2X2Icon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/AdjustmentsHorizontalIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ChevronDownIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FunnelIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/XMarkIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowUpIcon.js\");\n/* harmony import */ var _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AdjustmentsHorizontalIcon,ArchiveBoxIcon,ArrowDownIcon,ArrowUpIcon,ChevronDownIcon,DocumentDuplicateIcon,DocumentTextIcon,EllipsisVerticalIcon,EyeIcon,EyeSlashIcon,FunnelIcon,ListBulletIcon,MagnifyingGlassIcon,PencilIcon,PlusIcon,PowerIcon,Squares2X2Icon,TrashIcon,XMarkIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowDownIcon.js\");\n/* harmony import */ var _blog_modal__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./blog-modal */ \"(app-pages-browser)/./src/components/admin/blog/blog-modal.tsx\");\n/* harmony import */ var _components_providers_notification_provider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/providers/notification-provider */ \"(app-pages-browser)/./src/components/providers/notification-provider.tsx\");\n/* harmony import */ var _shared_confirmation_modal__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../shared/confirmation-modal */ \"(app-pages-browser)/./src/components/admin/shared/confirmation-modal.tsx\");\n/* __next_internal_client_entry_do_not_use__ BlogsManagement auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\nfunction BlogAvatar(param) {\n    let { title, featuredImageUrl, size = 'md', className = '', style = {} } = param;\n    _s();\n    const [imageError, setImageError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [imageLoading, setImageLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    // Size configurations\n    const sizeClasses = {\n        xs: 'w-6 h-6',\n        sm: 'w-8 h-8',\n        md: 'w-12 h-12',\n        lg: 'w-16 h-16',\n        xl: 'w-24 h-24',\n        'full-height': 'w-full h-full'\n    };\n    const textSizes = {\n        xs: 'text-xs',\n        sm: 'text-sm',\n        md: 'text-base',\n        lg: 'text-lg',\n        xl: 'text-xl',\n        'full-height': 'text-4xl'\n    };\n    // Generate initials from blog title\n    const getInitials = (title)=>{\n        return title.split(' ').map((word)=>word.charAt(0)).join('').toUpperCase().slice(0, 2);\n    };\n    // Generate a consistent color based on the blog title\n    const getBackgroundColor = (title)=>{\n        const colors = [\n            'bg-blue-500',\n            'bg-green-500',\n            'bg-purple-500',\n            'bg-pink-500',\n            'bg-indigo-500',\n            'bg-yellow-500',\n            'bg-red-500',\n            'bg-teal-500',\n            'bg-orange-500',\n            'bg-cyan-500'\n        ];\n        let hash = 0;\n        for(let i = 0; i < title.length; i++){\n            hash = title.charCodeAt(i) + ((hash << 5) - hash);\n        }\n        return colors[Math.abs(hash) % colors.length];\n    };\n    const handleImageLoad = ()=>{\n        setImageLoading(false);\n        setImageError(false);\n    };\n    const handleImageError = ()=>{\n        setImageLoading(false);\n        setImageError(true);\n    };\n    const baseClasses = \"\\n    \".concat(sizeClasses[size], \" \\n    rounded-lg \\n    flex \\n    items-center \\n    justify-center \\n    overflow-hidden \\n    \").concat(size === 'full-height' ? 'min-h-[320px]' : '', \"\\n    \").concat(className, \"\\n  \");\n    // If we have a valid featured image URL and no error, show the image\n    if (featuredImageUrl && !imageError) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"\".concat(baseClasses, \" bg-gray-100 relative\"),\n            style: style,\n            children: [\n                imageLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 flex items-center justify-center bg-gray-100\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 w-6 h-6\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: featuredImageUrl,\n                    alt: \"\".concat(title, \" featured image\"),\n                    className: \"\\n            w-full h-full object-cover\\n            \".concat(imageLoading ? 'opacity-0' : 'opacity-100', \"\\n            transition-opacity duration-200\\n          \"),\n                    onLoad: handleImageLoad,\n                    onError: handleImageError\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 280,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 271,\n            columnNumber: 7\n        }, this);\n    }\n    // Fallback: Show initials with colored background\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"\\n        \".concat(baseClasses, \" \\n        \").concat(getBackgroundColor(title), \" \\n        text-white \\n        font-semibold \\n        \").concat(textSizes[size], \"\\n        shadow-sm\\n      \"),\n        style: style,\n        title: title,\n        children: size === 'full-height' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                    className: \"w-24 h-24 text-white opacity-80\"\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 311,\n                    columnNumber: 11\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center px-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-2xl font-bold mb-2\",\n                            children: getInitials(title)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-sm opacity-90 break-words line-clamp-3\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 310,\n            columnNumber: 9\n        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n            children: getInitials(title)\n        }, void 0, false, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 318,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n        lineNumber: 297,\n        columnNumber: 5\n    }, this);\n}\n_s(BlogAvatar, \"yA6MC4/13YXgE42AlKw5vrWMK58=\");\n_c = BlogAvatar;\nfunction BlogsManagement(param) {\n    let { config } = param;\n    var _config_defaultSort, _config_defaultSort1, _config_defaultViewSettings, _config_defaultViewSettings1, _config_defaultViewSettings2, _config_defaultViewSettings3, _config_defaultViewSettings4, _config_filters, _config_filters1, _config_permissions, _deleteConfirmation_bulkPosts, _deleteConfirmation_bulkPosts1, _deleteConfirmation_post;\n    _s1();\n    // Notification system\n    const { showSuccess, showError, showInfo, showLoading } = (0,_components_providers_notification_provider__WEBPACK_IMPORTED_MODULE_4__.useNotifications)();\n    // State management\n    const [blogPosts, setBlogPosts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [searchQuery, setSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [debouncedSearchQuery, setDebouncedSearchQuery] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1);\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultSort = config.defaultSort) === null || _config_defaultSort === void 0 ? void 0 : _config_defaultSort.field) || 'updatedAt');\n    const [sortOrder, setSortOrder] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultSort1 = config.defaultSort) === null || _config_defaultSort1 === void 0 ? void 0 : _config_defaultSort1.direction) === 'asc' ? 'asc' : 'desc');\n    const [selectedPosts, setSelectedPosts] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([]);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultViewSettings = config.defaultViewSettings) === null || _config_defaultViewSettings === void 0 ? void 0 : _config_defaultViewSettings.mode) === 'list' || ((_config_defaultViewSettings1 = config.defaultViewSettings) === null || _config_defaultViewSettings1 === void 0 ? void 0 : _config_defaultViewSettings1.mode) === 'grid' ? config.defaultViewSettings.mode : 'list');\n    const [density, setDensity] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(((_config_defaultViewSettings2 = config.defaultViewSettings) === null || _config_defaultViewSettings2 === void 0 ? void 0 : _config_defaultViewSettings2.density) === 'compact' || ((_config_defaultViewSettings3 = config.defaultViewSettings) === null || _config_defaultViewSettings3 === void 0 ? void 0 : _config_defaultViewSettings3.density) === 'comfortable' ? config.defaultViewSettings.density : 'comfortable');\n    const [activeActionMenu, setActiveActionMenu] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [visibleColumns, setVisibleColumns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(Array.isArray((_config_defaultViewSettings4 = config.defaultViewSettings) === null || _config_defaultViewSettings4 === void 0 ? void 0 : _config_defaultViewSettings4.visibleColumns) ? config.defaultViewSettings.visibleColumns : []);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [actionLoading, setActionLoading] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    const [showColumnSelector, setShowColumnSelector] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [showWindowList, setShowWindowList] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [gridColumns, setGridColumns] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(3);\n    // Modal states\n    const [isCreateModalOpen, setIsCreateModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [isEditModalOpen, setIsEditModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [editingPost, setEditingPost] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(null);\n    // Confirmation modal state\n    const [deleteConfirmation, setDeleteConfirmation] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        isOpen: false,\n        post: null,\n        isBulkDelete: false,\n        bulkPosts: []\n    });\n    // Debounce search query\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BlogsManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"BlogsManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchQuery(searchQuery);\n                    setCurrentPage(1); // Reset to first page when searching\n                }\n            }[\"BlogsManagement.useEffect.timer\"], 300);\n            return ({\n                \"BlogsManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"BlogsManagement.useEffect\"];\n        }\n    }[\"BlogsManagement.useEffect\"], [\n        searchQuery\n    ]);\n    // Handle clicking outside to close action menu\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BlogsManagement.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"BlogsManagement.useEffect.handleClickOutside\": (event)=>{\n                    if (activeActionMenu) {\n                        const target = event.target;\n                        const actionMenu = document.querySelector('[data-post-id=\"'.concat(activeActionMenu, '\"] .action-menu'));\n                        const actionButton = document.querySelector('[data-post-id=\"'.concat(activeActionMenu, '\"] .action-button'));\n                        if (actionMenu && actionButton && !actionMenu.contains(target) && !actionButton.contains(target)) {\n                            setActiveActionMenu(null);\n                        }\n                    }\n                }\n            }[\"BlogsManagement.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"BlogsManagement.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"BlogsManagement.useEffect\"];\n        }\n    }[\"BlogsManagement.useEffect\"], [\n        activeActionMenu\n    ]);\n    // Fetch blog posts\n    const fetchBlogPosts = async function() {\n        let preserveFocus = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : false;\n        setLoading(true);\n        if (!preserveFocus) {\n            showLoading('Loading Blog Posts', 'Retrieving blog posts...');\n        }\n        try {\n            const params = new URLSearchParams({\n                page: currentPage.toString(),\n                limit: (config.pageSize || 10).toString(),\n                search: debouncedSearchQuery,\n                sortBy,\n                sortOrder\n            });\n            // Add filters to params\n            Object.entries(filters).forEach((param)=>{\n                let [key, value] = param;\n                if (value) {\n                    params.append(key, value);\n                }\n            });\n            console.log('Fetching blog posts with params:', params.toString()); // Debug log\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"?\").concat(params));\n            if (!response.ok) {\n                const errorText = await response.text();\n                console.error('API Error Response:', errorText);\n                throw new Error(\"HTTP error! status: \".concat(response.status, \" - \").concat(errorText));\n            }\n            const data = await response.json();\n            console.log('Received blog posts data:', data); // Debug log\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to fetch blog posts');\n            }\n            setBlogPosts(data.posts || []);\n            setTotalPages(Math.ceil((data.totalCount || 0) / (config.pageSize || 10)));\n            setError(null); // Clear any previous errors on successful fetch\n            if (!preserveFocus) {\n                var _data_posts, _data_posts1;\n                showSuccess('Blog Posts Loaded', \"Loaded \".concat(((_data_posts = data.posts) === null || _data_posts === void 0 ? void 0 : _data_posts.length) || 0, \" blog post\").concat(((_data_posts1 = data.posts) === null || _data_posts1 === void 0 ? void 0 : _data_posts1.length) === 1 ? '' : 's'));\n            }\n        } catch (err) {\n            console.error('Error fetching blog posts:', err); // Debug log\n            setError(err instanceof Error ? err.message : 'Failed to fetch blog posts');\n            if (!preserveFocus) {\n                showError('Failed to Load Blog Posts', 'Unable to retrieve blog posts');\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"BlogsManagement.useEffect\": ()=>{\n            // Preserve focus when searching\n            const isSearching = debouncedSearchQuery !== '';\n            fetchBlogPosts(isSearching);\n        }\n    }[\"BlogsManagement.useEffect\"], [\n        currentPage,\n        debouncedSearchQuery,\n        sortBy,\n        sortOrder,\n        filters\n    ]);\n    // Handle create\n    const handleCreate = async (formData)=>{\n        try {\n            showLoading('Creating Blog Post', 'Saving new blog post...');\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint), {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to create blog post (\".concat(response.status, \")\"));\n            }\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to create blog post');\n            }\n            setIsCreateModalOpen(false);\n            fetchBlogPosts();\n            showSuccess('Blog Post Created', '\"'.concat(formData.title || 'New blog post', '\" created successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to create blog post';\n            setError(errorMessage);\n            showError('Failed to Create Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Handle add button click\n    const handleAddClick = ()=>{\n        showInfo('Opening Create Form', 'Preparing to create a new blog post...');\n        setIsCreateModalOpen(true);\n    };\n    // Handle update\n    const handleUpdate = async (id, formData)=>{\n        try {\n            showLoading('Updating Blog Post', 'Saving changes...');\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                method: 'PUT',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify(formData)\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                throw new Error(data.error || \"Failed to update blog post (\".concat(response.status, \")\"));\n            }\n            if (!data.success) {\n                throw new Error(data.error || 'Failed to update blog post');\n            }\n            setIsEditModalOpen(false);\n            setEditingPost(null);\n            fetchBlogPosts();\n            showSuccess('Blog Post Updated', '\"'.concat(formData.title || 'Blog post', '\" updated successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to update blog post';\n            setError(errorMessage);\n            showError('Failed to Update Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Handle delete\n    const handleDelete = async (id)=>{\n        try {\n            const postToDelete = blogPosts.find((post)=>post.id === id);\n            showLoading('Deleting Blog Post', 'Removing \"'.concat((postToDelete === null || postToDelete === void 0 ? void 0 : postToDelete.title) || 'blog post', '\"...'));\n            const response = await fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                method: 'DELETE'\n            });\n            if (!response.ok) throw new Error('Failed to delete blog post');\n            fetchBlogPosts();\n            showSuccess('Blog Post Deleted', '\"'.concat((postToDelete === null || postToDelete === void 0 ? void 0 : postToDelete.title) || 'Blog post', '\" deleted successfully!'));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to delete blog post';\n            setError(errorMessage);\n            showError('Failed to Delete Blog Post', errorMessage);\n            throw err;\n        }\n    };\n    // Show delete confirmation\n    const showDeleteConfirmation = (post)=>{\n        setDeleteConfirmation({\n            isOpen: true,\n            post,\n            isBulkDelete: false,\n            bulkPosts: []\n        });\n    };\n    // Show bulk delete confirmation\n    const showBulkDeleteConfirmation = ()=>{\n        const postsToDelete = blogPosts.filter((post)=>selectedPosts.includes(post.id));\n        setDeleteConfirmation({\n            isOpen: true,\n            post: null,\n            isBulkDelete: true,\n            bulkPosts: postsToDelete\n        });\n    };\n    // Confirm delete\n    const confirmDelete = async ()=>{\n        try {\n            if (deleteConfirmation.isBulkDelete && deleteConfirmation.bulkPosts) {\n                // Bulk delete\n                const promises = deleteConfirmation.bulkPosts.map((post)=>fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(post.id), {\n                        method: 'DELETE'\n                    }));\n                await Promise.all(promises);\n                setSelectedPosts([]);\n                fetchBlogPosts();\n                showSuccess('Blog Posts Deleted', \"\".concat(deleteConfirmation.bulkPosts.length, \" blog post(s) deleted successfully!\"));\n            } else if (deleteConfirmation.post) {\n                // Single delete\n                await handleDelete(deleteConfirmation.post.id);\n            }\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : 'Failed to delete blog post(s)';\n            showError('Failed to Delete Blog Post(s)', errorMessage);\n        } finally{\n            setDeleteConfirmation({\n                isOpen: false,\n                post: null,\n                isBulkDelete: false,\n                bulkPosts: []\n            });\n        }\n    };\n    // Cancel delete\n    const cancelDelete = ()=>{\n        setDeleteConfirmation({\n            isOpen: false,\n            post: null,\n            isBulkDelete: false,\n            bulkPosts: []\n        });\n    };\n    // Handle bulk actions\n    const handleBulkAction = async (action)=>{\n        if (selectedPosts.length === 0) return;\n        setActionLoading(action);\n        try {\n            if (action === 'delete') {\n                showBulkDeleteConfirmation();\n                return;\n            }\n            showLoading(\"Bulk \".concat(action), \"Processing \".concat(selectedPosts.length, \" blog post\").concat(selectedPosts.length === 1 ? '' : 's', \"...\"));\n            const promises = selectedPosts.map(async (id)=>{\n                switch(action){\n                    case 'publish':\n                        return fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                            method: 'PUT',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                isPublished: true\n                            })\n                        });\n                    case 'unpublish':\n                        return fetch(\"/api/admin/\".concat(config.endpoint, \"/\").concat(id), {\n                            method: 'PUT',\n                            headers: {\n                                'Content-Type': 'application/json'\n                            },\n                            body: JSON.stringify({\n                                isPublished: false\n                            })\n                        });\n                    default:\n                        throw new Error(\"Unknown bulk action: \".concat(action));\n                }\n            });\n            await Promise.all(promises);\n            setSelectedPosts([]);\n            fetchBlogPosts();\n            showSuccess(\"Bulk \".concat(action, \" completed\"), \"\".concat(selectedPosts.length, \" blog post\").concat(selectedPosts.length === 1 ? '' : 's', \" \").concat(action, \"ed successfully!\"));\n        } catch (err) {\n            const errorMessage = err instanceof Error ? err.message : \"Failed to \".concat(action, \" blog posts\");\n            setError(errorMessage);\n            showError(\"Failed to \".concat(action, \" blog posts\"), errorMessage);\n        } finally{\n            setActionLoading(null);\n        }\n    };\n    // Handle individual actions\n    const handleAction = async (action, item)=>{\n        setActionLoading(\"\".concat(action, \"-\").concat(item.id));\n        try {\n            switch(action){\n                case 'edit':\n                    showInfo('Opening Editor', 'Editing \"'.concat(item.title, '\"'));\n                    setEditingPost(item);\n                    setIsEditModalOpen(true);\n                    break;\n                case 'view':\n                    showInfo('Opening View', 'Viewing \"'.concat(item.title, '\"'));\n                    // TODO: Implement view functionality\n                    showInfo('View Blog Post', 'Opening \"'.concat(item.title, '\" in new tab'));\n                    break;\n                case 'delete':\n                    showInfo('Delete Confirmation', 'Preparing to delete \"'.concat(item.title, '\"'));\n                    showDeleteConfirmation(item);\n                    break;\n                case 'toggle-published':\n                    const newStatus = !item.isPublished;\n                    showLoading(newStatus ? 'Publishing Blog Post' : 'Unpublishing Blog Post', \"\".concat(newStatus ? 'Publishing' : 'Unpublishing', ' \"').concat(item.title, '\"...'));\n                    await handleUpdate(item.id, {\n                        isPublished: newStatus\n                    });\n                    showSuccess(newStatus ? 'Blog Post Published' : 'Blog Post Unpublished', '\"'.concat(item.title, '\" ').concat(newStatus ? 'published' : 'unpublished', \" successfully!\"));\n                    break;\n                case 'duplicate':\n                    showLoading('Duplicating Blog Post', 'Creating copy of \"'.concat(item.title, '\"...'));\n                    // TODO: Implement duplicate functionality\n                    showSuccess('Blog Post Duplicated', '\"'.concat(item.title, '\" duplicated successfully!'));\n                    break;\n                case 'archive':\n                    showLoading('Archiving Blog Post', 'Archiving \"'.concat(item.title, '\"...'));\n                    // TODO: Implement archive functionality\n                    showSuccess('Blog Post Archived', '\"'.concat(item.title, '\" archived successfully!'));\n                    break;\n                default:\n                    console.warn(\"Unknown action: \".concat(action));\n            }\n        } finally{\n            setActionLoading(null);\n        }\n    };\n    // Handle sorting\n    const handleSort = (field)=>{\n        const newOrder = sortBy === field ? sortOrder === 'asc' ? 'desc' : 'asc' : 'asc';\n        const newField = sortBy === field ? field : field;\n        setSortBy(newField);\n        setSortOrder(newOrder);\n        setCurrentPage(1);\n        showInfo('Sorting Blog Posts', \"Sorting by \".concat(field, \" (\").concat(newOrder, \")\"));\n    };\n    // Handle selection\n    const handleSelectAll = (checked)=>{\n        if (checked) {\n            setSelectedPosts(blogPosts.map((post)=>post.id));\n            showInfo('Selection Updated', \"Selected all \".concat(blogPosts.length, \" blog post\").concat(blogPosts.length === 1 ? '' : 's'));\n        } else {\n            setSelectedPosts([]);\n            showInfo('Selection Cleared', 'Deselected all blog posts');\n        }\n    };\n    const handleSelectPost = (id, checked)=>{\n        if (checked) {\n            setSelectedPosts([\n                ...selectedPosts,\n                id\n            ]);\n            const post = blogPosts.find((p)=>p.id === id);\n            showInfo('Post Selected', '\"'.concat((post === null || post === void 0 ? void 0 : post.title) || 'Blog post', '\" added to selection'));\n        } else {\n            setSelectedPosts(selectedPosts.filter((postId)=>postId !== id));\n            const post = blogPosts.find((p)=>p.id === id);\n            showInfo('Post Deselected', '\"'.concat((post === null || post === void 0 ? void 0 : post.title) || 'Blog post', '\" removed from selection'));\n        }\n    };\n    // Get visible fields for table\n    const getVisibleFields = ()=>{\n        if (visibleColumns.length > 0) {\n            var _config_fields;\n            return ((_config_fields = config.fields) === null || _config_fields === void 0 ? void 0 : _config_fields.filter((field)=>visibleColumns.includes(field.key))) || [];\n        }\n        return config.fields || [];\n    };\n    // Handle view mode change\n    const handleViewModeChange = (mode)=>{\n        setViewMode(mode);\n        showInfo('View Mode Changed', \"Switched to \".concat(mode, \" view\"));\n    };\n    // Handle density change\n    const handleDensityChange = (newDensity)=>{\n        setDensity(newDensity);\n        showInfo('Density Updated', \"Changed to \".concat(newDensity, \" density\"));\n    };\n    // Handle grid columns change\n    const handleGridColumnsChange = (columns)=>{\n        setGridColumns(columns);\n        showInfo('Grid Layout Updated', \"Changed to \".concat(columns, \" column\").concat(columns === 1 ? '' : 's', \" layout\"));\n    };\n    // Handle filter changes\n    const handleFilterChange = (key, value)=>{\n        const newFilters = {\n            ...filters\n        };\n        if (value) {\n            newFilters[key] = value;\n        } else {\n            delete newFilters[key];\n        }\n        setFilters(newFilters);\n        showInfo('Filter Applied', \"Filter updated: \".concat(key, \" = \").concat(value || 'all'));\n    };\n    // Close all action menus\n    const closeAllActionMenus = ()=>{\n        setActiveActionMenu(null);\n    };\n    // Grid Card Component\n    const GridCard = (param)=>{\n        let { post } = param;\n        const isSelected = selectedPosts.includes(post.id);\n        const classes = getGridDensityClasses();\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            \"data-post-id\": post.id,\n            className: \"group relative bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 \".concat(isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : '', \" \").concat(classes.card),\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"aspect-video bg-gray-100 rounded-t-lg overflow-hidden\",\n                    children: post.featuredImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: post.featuredImageUrl,\n                        alt: post.title,\n                        className: \"w-full h-full object-cover\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 826,\n                        columnNumber: 13\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full h-full flex items-center justify-center\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                            className: \"w-12 h-12 text-gray-400\"\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 833,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 832,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 824,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: classes.content,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"\".concat(classes.title, \" text-gray-900 line-clamp-2\"),\n                            children: post.title\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 841,\n                            columnNumber: 11\n                        }, this),\n                        post.excerpt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"\".concat(classes.excerpt, \" text-gray-600 line-clamp-3\"),\n                            children: post.excerpt\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 847,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: classes.meta,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex \".concat(classes.status, \" font-semibold rounded-full \").concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                            children: post.isPublished ? 'Published' : 'Draft'\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 856,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"\".concat(classes.date, \" text-gray-500\"),\n                                            children: formatDate(post.updatedAt)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 863,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 855,\n                                    columnNumber: 17\n                                }, this),\n                                post.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap \".concat(classes.categories),\n                                    children: post.categories.split(',').slice(0, 2).map((category, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"inline-flex \".concat(classes.category, \" bg-blue-100 text-blue-800 rounded\"),\n                                            children: category.trim()\n                                        }, index, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 872,\n                                            columnNumber: 23\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 870,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 853,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between \".concat(density === 'compact' ? 'mt-3 pt-2' : 'mt-4 pt-3', \" border-t border-gray-200\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: [\n                                        \"Updated \",\n                                        formatDate(post.updatedAt)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 882,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"lg:hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"action-button p-2 text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors\",\n                                        onClick: (e)=>{\n                                            e.stopPropagation();\n                                            const isVisible = activeActionMenu === post.id;\n                                            if (isVisible) {\n                                                // Hide menu\n                                                setActiveActionMenu(null);\n                                            } else {\n                                                // Show menu (this will automatically hide any other open menu)\n                                                setActiveActionMenu(post.id);\n                                            }\n                                        },\n                                        title: \"Show Actions\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                            className: \"h-4 w-4\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 903,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 888,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 887,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 881,\n                            columnNumber: 15\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"action-menu absolute bg-white rounded-lg border border-gray-200 shadow-lg flex flex-col items-center justify-center transition-all duration-200 z-50 \".concat(density === 'compact' ? 'top-1 right-1 bottom-1 w-12 space-y-1.5 px-1' : 'top-2 right-2 bottom-2 w-14 space-y-2 px-1.5'),\n                            style: {\n                                opacity: activeActionMenu === post.id ? '1' : '0',\n                                transform: activeActionMenu === post.id ? 'translateX(0)' : 'translateX(100%)',\n                                pointerEvents: activeActionMenu === post.id ? 'auto' : 'none'\n                            },\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeAllActionMenus();\n                                        handleAction('edit', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 border border-blue-500 hover:border-blue-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : 'w-10 h-10'),\n                                    title: \"Edit Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 930,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 919,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeAllActionMenus();\n                                        handleAction('view', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-indigo-600 hover:bg-indigo-700 border border-indigo-500 hover:border-indigo-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : 'w-10 h-10'),\n                                    title: \"View Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 945,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 934,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeAllActionMenus();\n                                        handleAction('toggle-published', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center \".concat(post.isPublished ? 'bg-orange-600 hover:bg-orange-700 border-orange-500 hover:border-orange-600' : 'bg-green-600 hover:bg-green-700 border-green-500 hover:border-green-600', \" border text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \").concat(density === 'compact' ? 'w-8 h-8' : 'w-10 h-10'),\n                                    title: post.isPublished ? 'Unpublish Blog Post' : 'Publish Blog Post',\n                                    children: post.isPublished ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 965,\n                                        columnNumber: 17\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 967,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 949,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeAllActionMenus();\n                                        handleAction('duplicate', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-purple-600 hover:bg-purple-700 border border-purple-500 hover:border-purple-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : 'w-10 h-10'),\n                                    title: \"Duplicate Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 983,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 972,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeAllActionMenus();\n                                        handleAction('archive', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-yellow-600 hover:bg-yellow-700 border border-yellow-500 hover:border-yellow-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: \"Archive Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 998,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 987,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: (e)=>{\n                                        e.stopPropagation();\n                                        closeAllActionMenus();\n                                        handleAction('delete', post);\n                                    },\n                                    className: \"group/btn relative inline-flex items-center justify-center bg-red-600 hover:bg-red-700 border border-red-500 hover:border-red-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto \".concat(density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'),\n                                    title: \"Delete Blog Post\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                        className: \"group-hover/btn:scale-110 \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5')\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1013,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1002,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 909,\n                            columnNumber: 11\n                        }, this),\n                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute top-2 left-2 \".concat(classes.checkbox),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"checkbox\",\n                                checked: isSelected,\n                                onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                className: \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white \".concat(classes.checkbox),\n                                style: {\n                                    backgroundColor: 'white'\n                                }\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1021,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1020,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 839,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n            lineNumber: 817,\n            columnNumber: 7\n        }, this);\n    };\n    // Grid density styling helper\n    const getGridDensityClasses = ()=>{\n        const baseClasses = {\n            container: {\n                compact: 'p-1',\n                comfortable: 'p-3'\n            },\n            grid: {\n                compact: 'gap-2',\n                comfortable: 'gap-3'\n            },\n            card: {\n                compact: 'p-1',\n                comfortable: 'p-2'\n            },\n            content: {\n                compact: 'p-1',\n                comfortable: 'p-2'\n            },\n            title: {\n                compact: 'text-sm font-semibold mb-0.5',\n                comfortable: 'text-lg font-semibold mb-1'\n            },\n            excerpt: {\n                compact: 'text-xs mb-1',\n                comfortable: 'text-sm mb-1.5'\n            },\n            meta: {\n                compact: 'space-y-0.5 mb-1',\n                comfortable: 'space-y-1 mb-2'\n            },\n            status: {\n                compact: 'px-1 py-0.5 text-xs',\n                comfortable: 'px-1.5 py-0.5 text-xs'\n            },\n            date: {\n                compact: 'text-xs',\n                comfortable: 'text-xs'\n            },\n            categories: {\n                compact: 'gap-0.5',\n                comfortable: 'gap-0.5'\n            },\n            category: {\n                compact: 'px-1 py-0.5 text-xs',\n                comfortable: 'px-1.5 py-0.5 text-xs'\n            },\n            actions: {\n                compact: 'pt-1',\n                comfortable: 'pt-1.5'\n            },\n            buttons: {\n                compact: 'space-x-0.5',\n                comfortable: 'space-x-1'\n            },\n            button: {\n                compact: 'p-0.5',\n                comfortable: 'p-1'\n            },\n            icon: {\n                compact: 'w-2.5 h-2.5',\n                comfortable: 'w-3 h-3'\n            },\n            checkbox: {\n                compact: 'h-2.5 w-2.5',\n                comfortable: 'h-3 w-3'\n            }\n        };\n        return {\n            container: baseClasses.container[density],\n            grid: baseClasses.grid[density],\n            card: baseClasses.card[density],\n            content: baseClasses.content[density],\n            title: baseClasses.title[density],\n            excerpt: baseClasses.excerpt[density],\n            meta: baseClasses.meta[density],\n            status: baseClasses.status[density],\n            date: baseClasses.date[density],\n            categories: baseClasses.categories[density],\n            category: baseClasses.category[density],\n            actions: baseClasses.actions[density],\n            buttons: baseClasses.buttons[density],\n            button: baseClasses.button[density],\n            icon: baseClasses.icon[density],\n            checkbox: baseClasses.checkbox[density]\n        };\n    };\n    // Format date\n    const formatDate = (dateString)=>{\n        return new Date(dateString).toLocaleDateString();\n    };\n    // Truncate text\n    const truncateText = (text, maxLength)=>{\n        if (text.length <= maxLength) return text;\n        return text.substring(0, maxLength) + '...';\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"h-full flex flex-col space-y-4\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"2a24ec3e6d4b7b34\",\n                children: \".action-menu{transition:all.2s ease-in-out}@media(min-width:1025px){.group:hover .action-menu{opacity:1!important;transform:translatex(0)!important;pointer-events:auto!important}}.density-compact th,.density-compact td{padding-top:4px!important;padding-bottom:4px!important}.density-comfortable th,.density-comfortable td{padding-top:16px!important;padding-bottom:16px!important}.density-spacious th,.density-spacious td{padding-top:32px!important;padding-bottom:32px!important}\"\n            }, void 0, false, void 0, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"relative rounded-lg shadow-sm border border-gray-200/50 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-indigo-50/20\"\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 1167,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"relative p-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center justify-between\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center space-x-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"h-14 w-14 text-lime-600 -mt-2\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1172,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2a24ec3e6d4b7b34\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-3xl font-bold text-gray-900 mt-2\",\n                                                    children: \"Blog Management\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1174,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-sm font-medium text-gray-600\",\n                                                    children: \"Create, edit, and manage your blog content.\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1177,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1173,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1171,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"hidden lg:flex items-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleAddClick,\n                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors shadow-sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1188,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Add Blog Post\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1184,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1183,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1170,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                        lineNumber: 1169,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 1166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                role: \"main\",\n                \"aria-label\": \"Blog management section\",\n                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex-1 rounded-lg shadow-sm border border-gray-200/50 overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        paddingBottom: '0'\n                    },\n                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"space-y-3 p-2 bg-white rounded-lg border border-gray-200 shadow-sm\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex flex-col space-y-3 lg:hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"w-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"relative\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                        className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1211,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                        type: \"text\",\n                                                        placeholder: \"Search blog posts...\",\n                                                        value: searchQuery,\n                                                        onChange: (e)=>setSearchQuery(e.target.value),\n                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"pl-10 pr-4 py-2.5 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1212,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1210,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1209,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center gap-1\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-0.5 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleViewModeChange('list'),\n                                                            title: \"List view\",\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 \".concat(viewMode === 'list' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1235,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-xs font-medium hidden xs:inline\",\n                                                                    children: \"List\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1236,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1226,\n                                                            columnNumber: 17\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>handleViewModeChange('grid'),\n                                                            title: \"Grid view\",\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 \".concat(viewMode === 'grid' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"h-3 w-3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1247,\n                                                                    columnNumber: 19\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-xs font-medium hidden xs:inline\",\n                                                                    children: \"Grid\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1248,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1238,\n                                                            columnNumber: 17\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1225,\n                                                    columnNumber: 15\n                                                }, this),\n                                                viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-0.5 flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-xs font-medium text-gray-700 px-1\",\n                                                            children: \"Col:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1255,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center gap-0.5 flex-1\",\n                                                            children: [\n                                                                1,\n                                                                2,\n                                                                3,\n                                                                4\n                                                            ].map((num)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(num),\n                                                                    title: \"\".concat(num, \" column\").concat(num > 1 ? 's' : ''),\n                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex-1 px-1.5 py-1 rounded text-xs font-medium \".concat(gridColumns === num ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: num\n                                                                }, num, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1258,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1256,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1254,\n                                                    columnNumber: 17\n                                                }, this),\n                                                viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowColumnSelector(!showColumnSelector),\n                                                        title: \"Columns\",\n                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1283,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"hidden xs:inline\",\n                                                                children: \"Col\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1284,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-3 w-3 ml-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1285,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1278,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1277,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowFilters(!showFilters),\n                                                        title: \"Filters\",\n                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium rounded-lg border \".concat(showFilters || Object.keys(filters).some((key)=>filters[key]) ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1301,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"hidden xs:inline\",\n                                                                children: \"Filter\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1302,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            Object.keys(filters).some((key)=>filters[key]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"ml-0.5 inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                children: Object.values(filters).filter(Boolean).length\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1304,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1292,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1291,\n                                                    columnNumber: 15\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"relative flex-1\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>setShowWindowList(!showWindowList),\n                                                        title: \"Density\",\n                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1318,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"hidden xs:inline\",\n                                                                children: density.charAt(0).toUpperCase() + density.slice(1)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1319,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-3 w-3 ml-0.5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1320,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1313,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1312,\n                                                    columnNumber: 15\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1223,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1207,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"hidden lg:flex items-center justify-between gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center gap-3 flex-1 max-w-md\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"relative flex-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1332,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                            type: \"text\",\n                                                            placeholder: \"Search blog posts by title, content, excerpt...\",\n                                                            value: searchQuery,\n                                                            onChange: (e)=>setSearchQuery(e.target.value),\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1333,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1331,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowFilters(!showFilters),\n                                                            title: \"Show/hide filters\",\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 \".concat(showFilters || Object.keys(filters).some((key)=>filters[key]) ? 'bg-blue-50 text-blue-700 border-blue-300' : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1353,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                \"Filters\",\n                                                                Object.keys(filters).some((key)=>filters[key]) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800\",\n                                                                    children: Object.values(filters).filter(Boolean).length\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1356,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1360,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1344,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"absolute top-full right-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"p-4\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center justify-between mb-4\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                                                children: \"Filters\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1368,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>setShowFilters(false),\n                                                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-gray-400 hover:text-gray-600\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                                    className: \"h-4 w-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1373,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1369,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1367,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"space-y-4\",\n                                                                        children: (_config_filters = config.filters) === null || _config_filters === void 0 ? void 0 : _config_filters.map((filter)=>{\n                                                                            var _filter_options;\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2a24ec3e6d4b7b34\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"block text-xs font-medium text-gray-700 mb-2\",\n                                                                                        children: filter.label\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1380,\n                                                                                        columnNumber: 33\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                                                        value: filters[filter.key] || '',\n                                                                                        onChange: (e)=>{\n                                                                                            const newFilters = {\n                                                                                                ...filters\n                                                                                            };\n                                                                                            if (e.target.value) {\n                                                                                                newFilters[filter.key] = e.target.value;\n                                                                                            } else {\n                                                                                                delete newFilters[filter.key];\n                                                                                            }\n                                                                                            setFilters(newFilters);\n                                                                                        },\n                                                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                                                        children: (_filter_options = filter.options) === null || _filter_options === void 0 ? void 0 : _filter_options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                                                value: option.value,\n                                                                                                className: \"jsx-2a24ec3e6d4b7b34\",\n                                                                                                children: option.label\n                                                                                            }, option.value, false, {\n                                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                                lineNumber: 1397,\n                                                                                                columnNumber: 37\n                                                                                            }, this))\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1383,\n                                                                                        columnNumber: 33\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, filter.key, true, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1379,\n                                                                                columnNumber: 31\n                                                                            }, this);\n                                                                        })\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1377,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex justify-end mt-4 pt-4 border-t border-gray-200\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>{\n                                                                                setFilters({});\n                                                                                setShowFilters(false);\n                                                                            },\n                                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg\",\n                                                                            children: \"Clear All\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1407,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1406,\n                                                                        columnNumber: 27\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1366,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1365,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1343,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1330,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center space-x-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-sm font-medium text-gray-700\",\n                                                            children: \"View:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1427,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-1 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleViewModeChange('list'),\n                                                                    title: \"List view\",\n                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"px-3 py-2 rounded-md flex items-center gap-2 \".concat(viewMode === 'list' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                            className: \"h-5 w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1438,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-sm font-medium\",\n                                                                            children: \"List\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1439,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1429,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleViewModeChange('grid'),\n                                                                    title: \"Grid view\",\n                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"px-3 py-2 rounded-md flex items-center gap-2 \".concat(viewMode === 'grid' ? 'bg-white text-blue-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-5 w-5\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1450,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-sm font-medium\",\n                                                                            children: \"Grid\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1451,\n                                                                            columnNumber: 25\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1441,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1428,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1426,\n                                                    columnNumber: 19\n                                                }, this),\n                                                viewMode === 'grid' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-sm font-medium text-gray-700\",\n                                                            children: \"Columns:\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1459,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center bg-gray-100 rounded-lg p-1 gap-1\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(1),\n                                                                    title: \"1 column\",\n                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 1 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1461,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(2),\n                                                                    title: \"2 columns\",\n                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 2 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1472,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(3),\n                                                                    title: \"3 columns\",\n                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 3 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1483,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                    onClick: ()=>handleGridColumnsChange(4),\n                                                                    title: \"4 columns\",\n                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"px-2 py-1 rounded text-xs font-medium \".concat(gridColumns === 4 ? 'bg-white text-green-600 shadow-sm' : 'text-gray-600 hover:text-gray-900'),\n                                                                    children: \"4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1494,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1460,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1458,\n                                                    columnNumber: 21\n                                                }, this),\n                                                viewMode === 'list' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowColumnSelector(!showColumnSelector),\n                                                            title: \"Select columns to display\",\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1517,\n                                                                    columnNumber: 25\n                                                                }, this),\n                                                                \"Columns\",\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1519,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1512,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        showColumnSelector && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"p-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-xs font-medium text-gray-500 uppercase tracking-wide mb-2\",\n                                                                        children: \"Show Columns\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1526,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    config.columns.map((column)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center space-x-2 py-1\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: \"checkbox\",\n                                                                                    checked: visibleColumns.includes(column.key),\n                                                                                    onChange: (e)=>{\n                                                                                        if (e.target.checked) {\n                                                                                            setVisibleColumns((prev)=>[\n                                                                                                    ...prev,\n                                                                                                    column.key\n                                                                                                ]);\n                                                                                        } else {\n                                                                                            setVisibleColumns((prev)=>prev.filter((col)=>col !== column.key));\n                                                                                        }\n                                                                                    },\n                                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1529,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-sm text-gray-700\",\n                                                                                    children: column.label\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1541,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, column.key, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1528,\n                                                                            columnNumber: 31\n                                                                        }, this))\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1525,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1524,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1511,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"relative dropdown-container\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                            onClick: ()=>setShowWindowList(!showWindowList),\n                                                            title: \"Select density\",\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                    className: \"h-4 w-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1559,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                density.charAt(0).toUpperCase() + density.slice(1),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                    className: \"h-4 w-4 ml-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1561,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1554,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        showWindowList && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"absolute top-full left-0 mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-50\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"p-1\",\n                                                                children: [\n                                                                    'compact',\n                                                                    'comfortable'\n                                                                ].map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                        onClick: ()=>{\n                                                                            handleDensityChange(option);\n                                                                            setShowWindowList(false);\n                                                                        },\n                                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 \".concat(density === option ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'),\n                                                                        children: option.charAt(0).toUpperCase() + option.slice(1)\n                                                                    }, option, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1569,\n                                                                        columnNumber: 29\n                                                                    }, this))\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1567,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1566,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1553,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1424,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1328,\n                                    columnNumber: 15\n                                }, this),\n                                showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center justify-between mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                    children: \"Filters\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1595,\n                                                    columnNumber: 21\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                    onClick: ()=>setShowFilters(false),\n                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-gray-400 hover:text-gray-600\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1600,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1596,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1594,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"space-y-4\",\n                                            children: (_config_filters1 = config.filters) === null || _config_filters1 === void 0 ? void 0 : _config_filters1.map((filter)=>{\n                                                var _filter_options;\n                                                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"block text-xs font-medium text-gray-700 mb-2\",\n                                                            children: filter.label\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1607,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                                            value: filters[filter.key] || '',\n                                                            onChange: (e)=>{\n                                                                const newFilters = {\n                                                                    ...filters\n                                                                };\n                                                                if (e.target.value) {\n                                                                    newFilters[filter.key] = e.target.value;\n                                                                } else {\n                                                                    delete newFilters[filter.key];\n                                                                }\n                                                                setFilters(newFilters);\n                                                            },\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500\",\n                                                            children: (_filter_options = filter.options) === null || _filter_options === void 0 ? void 0 : _filter_options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                                    value: option.value,\n                                                                    className: \"jsx-2a24ec3e6d4b7b34\",\n                                                                    children: option.label\n                                                                }, option.value, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1624,\n                                                                    columnNumber: 29\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1610,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, filter.key, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1606,\n                                                    columnNumber: 23\n                                                }, this);\n                                            })\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1604,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex justify-end mt-4 pt-4 border-t border-gray-200\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>{\n                                                    setFilters({});\n                                                    setShowFilters(false);\n                                                },\n                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg\",\n                                                children: \"Clear All\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1634,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1633,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1593,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1205,\n                            columnNumber: 11\n                        }, this),\n                        config.enableBulkActions && selectedPosts.length > 0 && (viewMode === 'list' || viewMode === 'grid') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"bg-blue-50 border border-blue-200 rounded-lg px-4 py-2 shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center space-x-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-blue-600 font-semibold text-xs\",\n                                                            children: selectedPosts.length\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1655,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1654,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-xs font-medium text-blue-900\",\n                                                        children: [\n                                                            \"blog post\",\n                                                            selectedPosts.length === 1 ? '' : 's',\n                                                            \" selected\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1659,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1653,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center space-x-1.5\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleBulkAction('publish'),\n                                                        title: \"Publish selected blog posts\",\n                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 border border-green-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1671,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Publish\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1666,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>handleBulkAction('unpublish'),\n                                                        title: \"Unpublish selected blog posts\",\n                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1680,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Unpublish\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1675,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>showBulkDeleteConfirmation(),\n                                                        title: \"Delete selected blog posts\",\n                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 border border-red-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 transition-colors duration-200\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-3 w-3 mr-1\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1689,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            \"Delete\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1684,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1665,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1652,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center space-x-1.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setSelectedPosts([]),\n                                            title: \"Clear selection\",\n                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gray-500 transition-colors duration-200\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                    className: \"h-3 w-3 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1702,\n                                                    columnNumber: 23\n                                                }, this),\n                                                \"Clear\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1697,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1696,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1651,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1650,\n                            columnNumber: 13\n                        }, this),\n                        error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"px-6 py-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"bg-red-50 border border-red-200 rounded-lg p-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex-shrink-0\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-5 w-5 text-red-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1716,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1715,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"ml-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-sm font-medium text-red-800\",\n                                                    children: \"Error\"\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1719,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"mt-2 text-sm text-red-700\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"jsx-2a24ec3e6d4b7b34\",\n                                                        children: error\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1721,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1720,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"mt-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            setError(null);\n                                                            fetchBlogPosts();\n                                                        },\n                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200\",\n                                                        children: \"Try Again\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1724,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1723,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1718,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1714,\n                                    columnNumber: 19\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1713,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1712,\n                            columnNumber: 15\n                        }, this),\n                        loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"px-6 py-8\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1744,\n                                        columnNumber: 19\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"ml-3 text-gray-600\",\n                                        children: \"Loading blog posts...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1745,\n                                        columnNumber: 19\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1743,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1742,\n                            columnNumber: 15\n                        }, this),\n                        !loading && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-2a24ec3e6d4b7b34\",\n                            children: blogPosts.length === 0 ? /* Empty State */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"p-12 text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                        className: \"mx-auto h-12 w-12 text-gray-400\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1756,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"mt-2 text-sm font-medium text-gray-900\",\n                                        children: \"No blog posts found\"\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1757,\n                                        columnNumber: 21\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"mt-1 text-sm text-gray-500\",\n                                        children: debouncedSearchQuery ? 'Try adjusting your search terms.' : 'Get started by creating your first blog post.'\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1758,\n                                        columnNumber: 21\n                                    }, this),\n                                    ((_config_permissions = config.permissions) === null || _config_permissions === void 0 ? void 0 : _config_permissions.create) && !debouncedSearchQuery && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"mt-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: handleAddClick,\n                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                                            children: \"Add Blog Post\"\n                                        }, void 0, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1763,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1762,\n                                        columnNumber: 23\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1755,\n                                columnNumber: 19\n                            }, this) : viewMode === 'list' ? /* Table View */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"overflow-hidden\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"overflow-x-auto\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"table\", {\n                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"min-w-full divide-y divide-gray-200 density-\".concat(density),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"thead\", {\n                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"bg-gray-200 border-b border-gray-300\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                    className: \"jsx-2a24ec3e6d4b7b34\",\n                                                    children: [\n                                                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            scope: \"col\",\n                                                            style: {\n                                                                width: '6px'\n                                                            },\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"relative pl-2 py-2\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                type: \"checkbox\",\n                                                                checked: selectedPosts.length === blogPosts.length && blogPosts.length > 0,\n                                                                onChange: (e)=>handleSelectAll(e.target.checked),\n                                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-blue-600 focus:ring-blue-500 border-gray-300 rounded \".concat(density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-5 w-5' : 'h-4 w-4')\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1782,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1781,\n                                                            columnNumber: 31\n                                                        }, this),\n                                                        getVisibleFields().map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                                scope: \"col\",\n                                                                onClick: ()=>handleSort(field.key),\n                                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"px-6 py-2 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 text-xs\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center space-x-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"jsx-2a24ec3e6d4b7b34\",\n                                                                            children: field.label\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1802,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        sortBy === field.key ? sortOrder === 'asc' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-black\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1805,\n                                                                            columnNumber: 39\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-black\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1807,\n                                                                            columnNumber: 39\n                                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                            className: \"h-3 w-3 text-gray-400\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1810,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1801,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            }, field.key, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1795,\n                                                                columnNumber: 31\n                                                            }, this)),\n                                                        config.actions && config.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"th\", {\n                                                            scope: \"col\",\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"px-6 py-2 text-right font-medium text-gray-900 uppercase tracking-wider text-xs min-w-[120px]\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"jsx-2a24ec3e6d4b7b34\",\n                                                                children: \"Actions\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1819,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1818,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                    lineNumber: 1778,\n                                                    columnNumber: 27\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1777,\n                                                columnNumber: 25\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tbody\", {\n                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"bg-white divide-y divide-gray-200\",\n                                                children: blogPosts.map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"tr\", {\n                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"hover:bg-gray-50 \".concat(selectedPosts.includes(post.id) ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''),\n                                                        children: [\n                                                            config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"px-6\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                    type: \"checkbox\",\n                                                                    checked: selectedPosts.includes(post.id),\n                                                                    onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                                                    style: {\n                                                                        backgroundColor: 'white'\n                                                                    },\n                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1832,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1831,\n                                                                columnNumber: 33\n                                                            }, this),\n                                                            getVisibleFields().map((field)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"px-6 whitespace-nowrap\",\n                                                                    children: field.key === 'title' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(BlogAvatar, {\n                                                                                title: post.title,\n                                                                                featuredImageUrl: post.featuredImageUrl,\n                                                                                size: \"sm\",\n                                                                                className: \"mr-3\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1847,\n                                                                                columnNumber: 39\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2a24ec3e6d4b7b34\",\n                                                                                children: [\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-sm font-medium text-gray-900\",\n                                                                                        children: post.title\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1854,\n                                                                                        columnNumber: 41\n                                                                                    }, this),\n                                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-sm text-gray-500\",\n                                                                                        children: post.slug\n                                                                                    }, void 0, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1855,\n                                                                                        columnNumber: 41\n                                                                                    }, this)\n                                                                                ]\n                                                                            }, void 0, true, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1853,\n                                                                                columnNumber: 39\n                                                                            }, this)\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1846,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'isPublished' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                        children: post.isPublished ? 'Published' : 'Draft'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1859,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'excerpt' || field.key === 'content' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        title: post[field.key],\n                                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-sm text-gray-900 max-w-xs truncate\",\n                                                                        children: truncateText(post[field.key] || '', 50)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1867,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'tags' || field.key === 'categories' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-sm text-gray-900\",\n                                                                        children: post[field.key] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex flex-wrap gap-1\",\n                                                                            children: [\n                                                                                post[field.key].split(',').slice(0, 2).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded\",\n                                                                                        children: tag.trim()\n                                                                                    }, index, false, {\n                                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                        lineNumber: 1875,\n                                                                                        columnNumber: 45\n                                                                                    }, this)),\n                                                                                post[field.key].split(',').length > 2 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-xs text-gray-500\",\n                                                                                    children: [\n                                                                                        \"+\",\n                                                                                        post[field.key].split(',').length - 2,\n                                                                                        \" more\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1880,\n                                                                                    columnNumber: 45\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1873,\n                                                                            columnNumber: 41\n                                                                        }, this) : '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1871,\n                                                                        columnNumber: 37\n                                                                    }, this) : field.key === 'createdAt' || field.key === 'updatedAt' || field.key === 'publishedAt' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-sm text-gray-500\",\n                                                                        children: post[field.key] ? formatDate(post[field.key]) : '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1886,\n                                                                        columnNumber: 37\n                                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-sm text-gray-900\",\n                                                                        children: post[field.key] || '-'\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                        lineNumber: 1890,\n                                                                        columnNumber: 37\n                                                                    }, this)\n                                                                }, field.key, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1844,\n                                                                    columnNumber: 33\n                                                                }, this)),\n                                                            config.actions && config.actions.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"td\", {\n                                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"px-6 whitespace-nowrap text-right text-sm font-medium\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center space-x-2\",\n                                                                    children: config.actions.map((action)=>{\n                                                                        const isLoading = actionLoading === \"\".concat(action.action, \"-\").concat(post.id);\n                                                                        const IconComponent = action.icon === 'EyeIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"] : action.icon === 'PencilIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"] : action.icon === 'PowerIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"] : action.icon === 'TrashIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"] : _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                            onClick: ()=>handleAction(action.action, post),\n                                                                            disabled: isLoading,\n                                                                            title: action.tooltip,\n                                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"p-1 rounded-md transition-colors \".concat(action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' : action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' : action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' : action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' : 'text-gray-600 hover:bg-gray-50', \" \").concat(isLoading ? 'opacity-50 cursor-not-allowed' : ''),\n                                                                            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"animate-spin rounded-full h-4 w-4 border-b-2 border-current\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1923,\n                                                                                columnNumber: 45\n                                                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"w-4 h-4\"\n                                                                            }, void 0, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 1925,\n                                                                                columnNumber: 45\n                                                                            }, this)\n                                                                        }, action.action, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1909,\n                                                                            columnNumber: 41\n                                                                        }, this);\n                                                                    })\n                                                                }, void 0, false, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1900,\n                                                                    columnNumber: 35\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1899,\n                                                                columnNumber: 33\n                                                            }, this)\n                                                        ]\n                                                    }, post.id, true, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1826,\n                                                        columnNumber: 29\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1824,\n                                                columnNumber: 25\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                        lineNumber: 1776,\n                                        columnNumber: 23\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1775,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1774,\n                                columnNumber: 19\n                            }, this) : viewMode === 'grid' ? /* Grid View - Restructured */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + (getGridDensityClasses().container || \"\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"grid \".concat(getGridDensityClasses().grid, \" \").concat(gridColumns === 1 ? 'grid-cols-1' : gridColumns === 2 ? 'grid-cols-1 md:grid-cols-2' : gridColumns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'),\n                                    children: blogPosts.map((post)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(GridCard, {\n                                            post: post\n                                        }, post.id, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1949,\n                                            columnNumber: 25\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1942,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1941,\n                                columnNumber: 19\n                            }, this) : /* Card View */ /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"p-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"space-y-4\",\n                                    children: blogPosts.map((post)=>{\n                                        var _config_actions;\n                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow \".concat(selectedPosts.includes(post.id) ? 'ring-2 ring-blue-500 bg-blue-50' : ''),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"w-48 h-32 bg-gray-100 rounded-l-lg overflow-hidden flex-shrink-0\",\n                                                        children: post.featuredImageUrl ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                            src: post.featuredImageUrl,\n                                                            alt: post.title,\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"w-full h-full object-cover\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1965,\n                                                            columnNumber: 33\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"w-full h-full flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"w-8 h-8 text-gray-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                lineNumber: 1972,\n                                                                columnNumber: 35\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1971,\n                                                            columnNumber: 33\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1963,\n                                                        columnNumber: 29\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex-1 p-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-start justify-between\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex-1\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-lg font-semibold text-gray-900 mb-1\",\n                                                                            children: post.title\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1982,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-sm text-gray-500 mb-2\",\n                                                                            children: post.slug\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1985,\n                                                                            columnNumber: 35\n                                                                        }, this),\n                                                                        post.excerpt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"text-sm text-gray-600 mb-3 line-clamp-2\",\n                                                                            children: post.excerpt\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1991,\n                                                                            columnNumber: 37\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center space-x-4 text-sm text-gray-500\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"inline-flex px-2 py-1 text-xs font-semibold rounded-full \".concat(post.isPublished ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'),\n                                                                                    children: post.isPublished ? 'Published' : 'Draft'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 1998,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-2a24ec3e6d4b7b34\",\n                                                                                    children: [\n                                                                                        \"Updated: \",\n                                                                                        formatDate(post.updatedAt)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2005,\n                                                                                    columnNumber: 37\n                                                                                }, this),\n                                                                                post.categories && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"jsx-2a24ec3e6d4b7b34\",\n                                                                                    children: [\n                                                                                        \"Category: \",\n                                                                                        post.categories.split(',')[0].trim()\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2007,\n                                                                                    columnNumber: 39\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 1997,\n                                                                            columnNumber: 35\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 1980,\n                                                                    columnNumber: 33\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"flex items-center space-x-2 ml-4\",\n                                                                    children: [\n                                                                        (_config_actions = config.actions) === null || _config_actions === void 0 ? void 0 : _config_actions.map((action)=>{\n                                                                            const isLoading = actionLoading === \"\".concat(action.action, \"-\").concat(post.id);\n                                                                            const IconComponent = action.icon === 'EyeIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"] : action.icon === 'PencilIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"] : action.icon === 'PowerIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"] : action.icon === 'TrashIcon' ? _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"] : _barrel_optimize_names_AdjustmentsHorizontalIcon_ArchiveBoxIcon_ArrowDownIcon_ArrowUpIcon_ChevronDownIcon_DocumentDuplicateIcon_DocumentTextIcon_EllipsisVerticalIcon_EyeIcon_EyeSlashIcon_FunnelIcon_ListBulletIcon_MagnifyingGlassIcon_PencilIcon_PlusIcon_PowerIcon_Squares2X2Icon_TrashIcon_XMarkIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n                                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                                onClick: ()=>handleAction(action.action, post),\n                                                                                disabled: isLoading,\n                                                                                title: action.tooltip,\n                                                                                className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"p-2 rounded-md transition-colors \".concat(action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' : action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' : action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' : action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' : 'text-gray-600 hover:bg-gray-50', \" \").concat(isLoading ? 'opacity-50 cursor-not-allowed' : ''),\n                                                                                children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"animate-spin rounded-full h-4 w-4 border-b-2 border-current\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2036,\n                                                                                    columnNumber: 43\n                                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(IconComponent, {\n                                                                                    className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"w-4 h-4\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                    lineNumber: 2038,\n                                                                                    columnNumber: 43\n                                                                                }, this)\n                                                                            }, action.action, false, {\n                                                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                                lineNumber: 2022,\n                                                                                columnNumber: 39\n                                                                            }, this);\n                                                                        }),\n                                                                        config.enableBulkActions && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: \"checkbox\",\n                                                                            checked: selectedPosts.includes(post.id),\n                                                                            onChange: (e)=>handleSelectPost(post.id, e.target.checked),\n                                                                            style: {\n                                                                                backgroundColor: 'white'\n                                                                            },\n                                                                            className: \"jsx-2a24ec3e6d4b7b34\" + \" \" + \"rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                            lineNumber: 2046,\n                                                                            columnNumber: 37\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                                    lineNumber: 2013,\n                                                                    columnNumber: 33\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                            lineNumber: 1979,\n                                                            columnNumber: 31\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                        lineNumber: 1978,\n                                                        columnNumber: 29\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                                lineNumber: 1961,\n                                                columnNumber: 27\n                                            }, this)\n                                        }, post.id, false, {\n                                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                            lineNumber: 1958,\n                                            columnNumber: 25\n                                        }, this);\n                                    })\n                                }, void 0, false, {\n                                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                    lineNumber: 1956,\n                                    columnNumber: 21\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                                lineNumber: 1955,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                            lineNumber: 1752,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                    lineNumber: 1203,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 1198,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_modal__WEBPACK_IMPORTED_MODULE_3__.BlogModal, {\n                isOpen: isCreateModalOpen,\n                onClose: ()=>setIsCreateModalOpen(false),\n                onSubmit: handleCreate,\n                title: \"Create New Blog Post\"\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 2069,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_blog_modal__WEBPACK_IMPORTED_MODULE_3__.BlogModal, {\n                isOpen: isEditModalOpen,\n                onClose: ()=>{\n                    setIsEditModalOpen(false);\n                    setEditingPost(null);\n                },\n                onSubmit: async (formData)=>{\n                    if (editingPost) {\n                        await handleUpdate(editingPost.id, formData);\n                    }\n                },\n                title: \"Edit Blog Post\",\n                initialData: editingPost !== null && editingPost !== void 0 ? editingPost : undefined\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 2076,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_shared_confirmation_modal__WEBPACK_IMPORTED_MODULE_5__.ConfirmationModal, {\n                isOpen: deleteConfirmation.isOpen,\n                title: \"Delete Confirmation\",\n                message: \"Are you sure you want to delete this blog post?\",\n                details: (()=>{\n                    if (deleteConfirmation.isBulkDelete) {\n                        var _deleteConfirmation_bulkPosts;\n                        const count = ((_deleteConfirmation_bulkPosts = deleteConfirmation.bulkPosts) === null || _deleteConfirmation_bulkPosts === void 0 ? void 0 : _deleteConfirmation_bulkPosts.length) || 0;\n                        return \"This action will permanently delete \".concat(count, \" blog post\").concat(count === 1 ? '' : 's', \". This cannot be undone.\");\n                    }\n                    const post = deleteConfirmation.post;\n                    return 'This action will permanently delete \"'.concat((post === null || post === void 0 ? void 0 : post.title) || 'this blog post', '\". This cannot be undone.');\n                })(),\n                confirmText: \"Delete\",\n                cancelText: \"Cancel\",\n                onConfirm: confirmDelete,\n                onCancel: cancelDelete,\n                type: \"danger\",\n                showVerification: true,\n                verificationData: {\n                    canDelete: true,\n                    reason: deleteConfirmation.isBulkDelete ? \"\".concat(((_deleteConfirmation_bulkPosts = deleteConfirmation.bulkPosts) === null || _deleteConfirmation_bulkPosts === void 0 ? void 0 : _deleteConfirmation_bulkPosts.length) || 0, \" blog post\").concat((((_deleteConfirmation_bulkPosts1 = deleteConfirmation.bulkPosts) === null || _deleteConfirmation_bulkPosts1 === void 0 ? void 0 : _deleteConfirmation_bulkPosts1.length) || 0) === 1 ? '' : 's', \" selected for deletion\") : 'Blog post \"'.concat(((_deleteConfirmation_post = deleteConfirmation.post) === null || _deleteConfirmation_post === void 0 ? void 0 : _deleteConfirmation_post.title) || 'Unknown', '\" ready for deletion')\n                }\n            }, void 0, false, {\n                fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n                lineNumber: 2092,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Volumes/Files/Technoloway-New-Website/Technoloway/src/components/admin/blog/blogs-management.tsx\",\n        lineNumber: 1136,\n        columnNumber: 5\n    }, this);\n}\n_s1(BlogsManagement, \"ggm4gqsBMvPb+FXTAqxKPBO0Zns=\", false, function() {\n    return [\n        _components_providers_notification_provider__WEBPACK_IMPORTED_MODULE_4__.useNotifications\n    ];\n});\n_c1 = BlogsManagement;\nvar _c, _c1;\n$RefreshReg$(_c, \"BlogAvatar\");\n$RefreshReg$(_c1, \"BlogsManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/admin/blog/blogs-management.tsx\n"));

/***/ })

});