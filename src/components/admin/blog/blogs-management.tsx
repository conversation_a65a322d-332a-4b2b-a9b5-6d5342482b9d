'use client';

import React, { useState, useEffect } from 'react';
import {
  EyeSlashIcon,
  EyeIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PencilIcon,
  TrashIcon,
  PowerIcon,
  DocumentTextIcon,
  DocumentDuplicateIcon,
  ArchiveBoxIcon,
  XMarkIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  ListBulletIcon,
  Squares2X2Icon,
  FunnelIcon,
  ChevronDownIcon,
  AdjustmentsHorizontalIcon,
  EllipsisVerticalIcon,
} from '@heroicons/react/24/outline';
import { BlogModal } from './blog-modal';
import { ReactNode } from 'react';
import { useNotifications } from '@/components/providers/notification-provider';
import { ConfirmationModal } from '../shared/confirmation-modal';

// Inline type definitions (extracted from crud/types.ts)
interface CrudColumn<T = any> {
  key: keyof T | string
  label: string
  sortable?: boolean
  searchable?: boolean
  renderType?: 'text' | 'email' | 'date' | 'currency' | 'boolean' | 'status' | 'rating' | 'progress' | 'image' | 'custom' | 'company' | 'number'
  renderFunction?: (value: any, row: T) => React.ReactNode
  renderProps?: any
  width?: string
  className?: string
  hideable?: boolean
  defaultVisible?: boolean
}

interface CrudField {
  key: string
  label: string
  name?: string
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'checkbox' | 'boolean' | 'radio' | 'date' | 'datetime-local' | 'url' | 'file'
  required?: boolean
  searchable?: boolean
  placeholder?: string
  defaultValue?: any
  options?: Array<{ value: string; label: string }>
  validation?: {
    minLength?: number
    maxLength?: number
    min?: number
    max?: number
    pattern?: RegExp
    step?: number
  }
  rows?: number
  multiple?: boolean
  accept?: string
}

interface CrudFilter {
  key: string
  label: string
  name?: string
  type: 'text' | 'select' | 'date' | 'daterange' | 'checkbox'
  options?: Array<{ value: string; label: string }>
  placeholder?: string
}

interface FormSection {
  title: string
  fields: string[]
}

interface FormLayout {
  type: 'compact' | 'full'
  columns: number
  sections: FormSection[]
}

interface CrudAction<T = any> {
  label: string
  icon: string
  action: 'edit' | 'delete' | 'view' | 'preview' | 'toggle-status' | 'toggle-published' | 'toggle-featured' | 'custom'
  customAction?: string
  variant?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning'
  disabled?: boolean
  hidden?: boolean
  requiresConfirmation?: boolean
  confirmationMessage?: string
  tooltip?: string
}

interface CrudBulkAction<T = any> {
  label: string
  icon?: string
  action: string
  variant?: 'primary' | 'secondary' | 'danger' | 'success' | 'warning'
  requiresConfirmation?: boolean
  confirmationMessage?: string
}

interface CrudPermissions {
  create: boolean
  read: boolean
  update: boolean
  delete: boolean
  export: boolean
}

type ViewMode = 'list' | 'grid'
type DisplayDensity = 'compact' | 'comfortable'

interface ViewSettings {
  mode: ViewMode
  density: DisplayDensity
  visibleColumns: string[]
}

interface CrudConfig<T = any> {
  title: string
  description?: string
  endpoint: string
  columns: CrudColumn<T>[]
  fields: CrudField[]
  filters?: CrudFilter[]
  actions?: CrudAction<T>[]
  bulkActions?: CrudBulkAction<T>[]
  permissions: CrudPermissions
  searchPlaceholder?: string
  defaultSort?: { field: string; direction: 'asc' | 'desc' }
  pageSize?: number
  enableSearch?: boolean
  enableFilters?: boolean
  enableBulkActions?: boolean
  enableExport?: boolean
  enableViewControls?: boolean
  enableDensityControls?: boolean
  enableColumnVisibility?: boolean
  defaultViewSettings?: Partial<ViewSettings>
  formLayout?: FormLayout
  customCreateButton?: ReactNode
  customHeader?: ReactNode
  customFooter?: ReactNode
  onItemClick?: boolean
}

interface BlogPost {
  id: string;
  title: string;
  slug: string;
  content: string;
  excerpt?: string;
  featuredImageUrl?: string;
  authorId?: string;
  isPublished: boolean;
  publishedAt?: string;
  categories?: string;
  tags?: string;
  createdAt: string;
  updatedAt: string;
  [key: string]: any;
}

interface BlogManagerProps {
  config: CrudConfig<BlogPost>;
}

// BlogAvatar Component
interface BlogAvatarProps {
  title: string;
  featuredImageUrl?: string | null;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | 'full-height';
  className?: string;
  style?: React.CSSProperties;
}

function BlogAvatar({ 
  title, 
  featuredImageUrl, 
  size = 'md', 
  className = '',
  style = {}
}: BlogAvatarProps) {
  const [imageError, setImageError] = useState(false);
  const [imageLoading, setImageLoading] = useState(true);

  // Size configurations
  const sizeClasses = {
    xs: 'w-6 h-6',
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24',
    'full-height': 'w-full h-full'
  };

  const textSizes = {
    xs: 'text-xs',
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    'full-height': 'text-4xl'
  };

  // Generate initials from blog title
  const getInitials = (title: string) => {
    return title
      .split(' ')
      .map(word => word.charAt(0))
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  // Generate a consistent color based on the blog title
  const getBackgroundColor = (title: string) => {
    const colors = [
      'bg-blue-500',
      'bg-green-500',
      'bg-purple-500',
      'bg-pink-500',
      'bg-indigo-500',
      'bg-yellow-500',
      'bg-red-500',
      'bg-teal-500',
      'bg-orange-500',
      'bg-cyan-500'
    ];
    
    let hash = 0;
    for (let i = 0; i < title.length; i++) {
      hash = title.charCodeAt(i) + ((hash << 5) - hash);
    }
    
    return colors[Math.abs(hash) % colors.length];
  };

  const handleImageLoad = () => {
    setImageLoading(false);
    setImageError(false);
  };

  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
  };

  const baseClasses = `
    ${sizeClasses[size]} 
    rounded-lg 
    flex 
    items-center 
    justify-center 
    overflow-hidden 
    ${size === 'full-height' ? 'min-h-[320px]' : ''}
    ${className}
  `;

  // If we have a valid featured image URL and no error, show the image
  if (featuredImageUrl && !imageError) {
    return (
      <div 
        className={`${baseClasses} bg-gray-100 relative`}
        style={style}
      >
        {imageLoading && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="animate-spin rounded-full border-2 border-gray-300 border-t-blue-600 w-6 h-6"></div>
          </div>
        )}
        <img
          src={featuredImageUrl}
          alt={`${title} featured image`}
          className={`
            w-full h-full object-cover
            ${imageLoading ? 'opacity-0' : 'opacity-100'}
            transition-opacity duration-200
          `}
          onLoad={handleImageLoad}
          onError={handleImageError}
        />
      </div>
    );
  }

  // Fallback: Show initials with colored background
  return (
    <div 
      className={`
        ${baseClasses} 
        ${getBackgroundColor(title)} 
        text-white 
        font-semibold 
        ${textSizes[size]}
        shadow-sm
      `}
      style={style}
      title={title}
    >
      {size === 'full-height' ? (
        <div className="flex flex-col items-center justify-center space-y-4">
          <DocumentTextIcon className="w-24 h-24 text-white opacity-80" />
          <div className="text-center px-4">
            <div className="text-2xl font-bold mb-2">{getInitials(title)}</div>
            <div className="text-sm opacity-90 break-words line-clamp-3">{title}</div>
          </div>
        </div>
      ) : (
        <span>{getInitials(title)}</span>
      )}
    </div>
  );
}

export function BlogsManagement({ config }: BlogManagerProps) {
  // Notification system
  const { showSuccess, showError, showInfo, showLoading } = useNotifications();
  
  // State management
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [sortBy, setSortBy] = useState(config.defaultSort?.field || 'updatedAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>(
    config.defaultSort?.direction === 'asc' ? 'asc' : 'desc'
  );
  const [selectedPosts, setSelectedPosts] = useState<string[]>([]);
  const [viewMode, setViewMode] = useState<'list' | 'grid'>(
    config.defaultViewSettings?.mode === 'list' ||
    config.defaultViewSettings?.mode === 'grid'
      ? config.defaultViewSettings.mode
      : 'list'
  );
  const [density, setDensity] = useState<'compact' | 'comfortable' | 'spacious'>(
    config.defaultViewSettings?.density === 'compact' || 
    config.defaultViewSettings?.density === 'comfortable' || 
    config.defaultViewSettings?.density === 'spacious'
      ? (config.defaultViewSettings.density as 'compact' | 'comfortable' | 'spacious')
      : 'comfortable'
  );
  const [activeActionMenu, setActiveActionMenu] = useState<string | null>(null);

  const [visibleColumns, setVisibleColumns] = useState<string[]>(
    Array.isArray(config.defaultViewSettings?.visibleColumns)
      ? config.defaultViewSettings.visibleColumns
      : []
  );
  const [filters, setFilters] = useState<Record<string, string>>({});
  const [showFilters, setShowFilters] = useState(false);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [showColumnSelector, setShowColumnSelector] = useState(false);
  const [showWindowList, setShowWindowList] = useState(false);
  const [gridColumns, setGridColumns] = useState(3);

  // Modal states
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [editingPost, setEditingPost] = useState<BlogPost | null>(null);
  
  // Confirmation modal state
  const [deleteConfirmation, setDeleteConfirmation] = useState<{
    isOpen: boolean;
    post: BlogPost | null;
    isBulkDelete?: boolean;
    bulkPosts?: BlogPost[];
  }>({
    isOpen: false,
    post: null,
    isBulkDelete: false,
    bulkPosts: []
  });

  // Debounce search query
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
      setCurrentPage(1); // Reset to first page when searching
    }, 300);

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Handle clicking outside to close action menu
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (activeActionMenu) {
        const target = event.target as Element;
        const actionMenu = document.querySelector(`[data-post-id="${activeActionMenu}"] .action-menu`);
        const actionButton = document.querySelector(`[data-post-id="${activeActionMenu}"] .action-button`);

        if (actionMenu && actionButton &&
            !actionMenu.contains(target) &&
            !actionButton.contains(target)) {
          setActiveActionMenu(null);
        }
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeActionMenu]);

  // Fetch blog posts
  const fetchBlogPosts = async (preserveFocus = false) => {
    setLoading(true);
    if (!preserveFocus) {
      showLoading('Loading Blog Posts', 'Retrieving blog posts...');
    }
    
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: (config.pageSize || 10).toString(),
        search: debouncedSearchQuery,
        sortBy,
        sortOrder,
      });

      // Add filters to params
      Object.entries(filters).forEach(([key, value]) => {
        if (value) {
          params.append(key, value);
        }
      });

      console.log('Fetching blog posts with params:', params.toString()); // Debug log

      const response = await fetch(`/api/admin/${config.endpoint}?${params}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('API Error Response:', errorText);
        throw new Error(`HTTP error! status: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('Received blog posts data:', data); // Debug log

      if (!data.success) {
        throw new Error(data.error || 'Failed to fetch blog posts');
      }

      setBlogPosts(data.posts || []);
      setTotalPages(Math.ceil((data.totalCount || 0) / (config.pageSize || 10)));
      setError(null); // Clear any previous errors on successful fetch
      
      if (!preserveFocus) {
        showSuccess('Blog Posts Loaded', `Loaded ${data.posts?.length || 0} blog post${data.posts?.length === 1 ? '' : 's'}`);
      }
    } catch (err) {
      console.error('Error fetching blog posts:', err); // Debug log
      setError(err instanceof Error ? err.message : 'Failed to fetch blog posts');
      if (!preserveFocus) {
        showError('Failed to Load Blog Posts', 'Unable to retrieve blog posts');
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Preserve focus when searching
    const isSearching = debouncedSearchQuery !== '';
    fetchBlogPosts(isSearching);
  }, [currentPage, debouncedSearchQuery, sortBy, sortOrder, filters]);

  // Handle create
  const handleCreate = async (formData: any) => {
    try {
      showLoading('Creating Blog Post', 'Saving new blog post...');
      
      const response = await fetch(`/api/admin/${config.endpoint}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || `Failed to create blog post (${response.status})`);
      }

      if (!data.success) {
        throw new Error(data.error || 'Failed to create blog post');
      }

      setIsCreateModalOpen(false);
      fetchBlogPosts();
      showSuccess('Blog Post Created', `"${formData.title || 'New blog post'}" created successfully!`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create blog post';
      setError(errorMessage);
      showError('Failed to Create Blog Post', errorMessage);
      throw err;
    }
  };

  // Handle add button click
  const handleAddClick = () => {
    showInfo('Opening Create Form', 'Preparing to create a new blog post...');
    setIsCreateModalOpen(true);
  };

  // Handle update
  const handleUpdate = async (id: string, formData: any) => {
    try {
      showLoading('Updating Blog Post', 'Saving changes...');
      
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(formData),
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || `Failed to update blog post (${response.status})`);
      }

      if (!data.success) {
        throw new Error(data.error || 'Failed to update blog post');
      }

      setIsEditModalOpen(false);
      setEditingPost(null);
      fetchBlogPosts();
      showSuccess('Blog Post Updated', `"${formData.title || 'Blog post'}" updated successfully!`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update blog post';
      setError(errorMessage);
      showError('Failed to Update Blog Post', errorMessage);
      throw err;
    }
  };

  // Handle delete
  const handleDelete = async (id: string) => {
    try {
      const postToDelete = blogPosts.find(post => post.id === id);
      showLoading('Deleting Blog Post', `Removing "${postToDelete?.title || 'blog post'}"...`);
      
      const response = await fetch(`/api/admin/${config.endpoint}/${id}`, {
        method: 'DELETE',
      });

      if (!response.ok) throw new Error('Failed to delete blog post');

      fetchBlogPosts();
      showSuccess('Blog Post Deleted', `"${postToDelete?.title || 'Blog post'}" deleted successfully!`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete blog post';
      setError(errorMessage);
      showError('Failed to Delete Blog Post', errorMessage);
      throw err;
    }
  };

  // Handle duplicate
  const handleDuplicate = async (post: BlogPost) => {
    try {
      showLoading('Duplicating Blog Post', `Creating copy of "${post.title}"...`);
      
      // Create duplicate data with modified title and slug
      const duplicateData = {
        title: `${post.title} (Copy)`,
        slug: `${post.slug}-copy-${Date.now()}`,
        content: post.content,
        excerpt: post.excerpt || '',
        featuredImageUrl: post.featuredImageUrl || '',
        authorId: post.authorId || '',
        isPublished: false, // Always create as draft
        publishedAt: null,
        categories: post.categories || '',
        tags: post.tags || ''
      };

      const response = await fetch(`/api/admin/${config.endpoint}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(duplicateData),
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || `Failed to duplicate blog post (${response.status})`);
      }

      if (!data.success) {
        throw new Error(data.error || 'Failed to duplicate blog post');
      }

      fetchBlogPosts();
      showSuccess('Blog Post Duplicated', `"${post.title}" duplicated successfully as "${duplicateData.title}"!`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to duplicate blog post';
      setError(errorMessage);
      showError('Failed to Duplicate Blog Post', errorMessage);
      throw err;
    }
  };

  // Handle archive
  const handleArchive = async (post: BlogPost) => {
    try {
      showLoading('Archiving Blog Post', `Archiving "${post.title}"...`);
      
      // Archive by unpublishing and adding archive tag
      const archiveData = {
        isPublished: false, // Unpublish the post
        tags: post.tags ? `${post.tags},archived` : 'archived',
        categories: post.categories || '',
        title: post.title,
        content: post.content,
        excerpt: post.excerpt || '',
        featuredImageUrl: post.featuredImageUrl || '',
        authorId: post.authorId || '',
        slug: post.slug
      };

      const response = await fetch(`/api/admin/${config.endpoint}/${post.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(archiveData),
      });

      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || `Failed to archive blog post (${response.status})`);
      }

      if (!data.success) {
        throw new Error(data.error || 'Failed to archive blog post');
      }

      fetchBlogPosts();
      showSuccess('Blog Post Archived', `"${post.title}" archived successfully!`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to archive blog post';
      setError(errorMessage);
      showError('Failed to Archive Blog Post', errorMessage);
      throw err;
    }
  };

  // Show delete confirmation
  const showDeleteConfirmation = (post: BlogPost) => {
    setDeleteConfirmation({
      isOpen: true,
      post,
      isBulkDelete: false,
      bulkPosts: []
    });
  };

  // Show bulk delete confirmation
  const showBulkDeleteConfirmation = () => {
    const postsToDelete = blogPosts.filter(post => selectedPosts.includes(post.id));
    setDeleteConfirmation({
      isOpen: true,
      post: postsToDelete.length === 1 ? postsToDelete[0] : null,
      isBulkDelete: true,
      bulkPosts: postsToDelete
    });
  };

  // Confirm delete
  const confirmDelete = async () => {
    try {
      if (deleteConfirmation.isBulkDelete && deleteConfirmation.bulkPosts) {
        // Bulk delete
        const promises = deleteConfirmation.bulkPosts.map(post => 
          fetch(`/api/admin/${config.endpoint}/${post.id}`, { method: 'DELETE' })
        );
        await Promise.all(promises);
        setSelectedPosts([]);
        fetchBlogPosts();
        showSuccess('Blog Posts Deleted', `${deleteConfirmation.bulkPosts.length} blog post(s) deleted successfully!`);
      } else if (deleteConfirmation.post) {
        // Single delete
        await handleDelete(deleteConfirmation.post.id);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete blog post(s)';
      showError('Failed to Delete Blog Post(s)', errorMessage);
    } finally {
      setDeleteConfirmation({
        isOpen: false,
        post: null,
        isBulkDelete: false,
        bulkPosts: []
      });
    }
  };

  // Cancel delete
  const cancelDelete = () => {
    setDeleteConfirmation({
      isOpen: false,
      post: null,
      isBulkDelete: false,
      bulkPosts: []
    });
  };

  // Handle bulk actions
  const handleBulkAction = async (action: string) => {
    if (selectedPosts.length === 0) return;

    setActionLoading(action);
    try {
      if (action === 'delete') {
        showBulkDeleteConfirmation();
        return;
      }

      showLoading(`Bulk ${action}`, `Processing ${selectedPosts.length} blog post${selectedPosts.length === 1 ? '' : 's'}...`);

      const promises = selectedPosts.map(async (id) => {
        switch (action) {
          case 'publish':
            return fetch(`/api/admin/${config.endpoint}/${id}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ isPublished: true }),
            });
          case 'unpublish':
            return fetch(`/api/admin/${config.endpoint}/${id}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ isPublished: false }),
            });
          case 'duplicate':
            const postToDuplicate = blogPosts.find(post => post.id === id);
            if (postToDuplicate) {
              const duplicateData = {
                title: `${postToDuplicate.title} (Copy)`,
                slug: `${postToDuplicate.slug}-copy-${Date.now()}`,
                content: postToDuplicate.content,
                excerpt: postToDuplicate.excerpt || '',
                featuredImageUrl: postToDuplicate.featuredImageUrl || '',
                authorId: postToDuplicate.authorId || '',
                isPublished: false,
                publishedAt: null,
                categories: postToDuplicate.categories || '',
                tags: postToDuplicate.tags || ''
              };
              return fetch(`/api/admin/${config.endpoint}`, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(duplicateData),
              });
            }
            throw new Error(`Post not found for duplication: ${id}`);
          case 'archive':
            const postToArchive = blogPosts.find(post => post.id === id);
            if (postToArchive) {
              const archiveData = {
                isPublished: false,
                tags: postToArchive.tags ? `${postToArchive.tags},archived` : 'archived',
                categories: postToArchive.categories || '',
                title: postToArchive.title,
                content: postToArchive.content,
                excerpt: postToArchive.excerpt || '',
                featuredImageUrl: postToArchive.featuredImageUrl || '',
                authorId: postToArchive.authorId || '',
                slug: postToArchive.slug
              };
              return fetch(`/api/admin/${config.endpoint}/${id}`, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(archiveData),
              });
            }
            throw new Error(`Post not found for archiving: ${id}`);
          default:
            throw new Error(`Unknown bulk action: ${action}`);
        }
      });

      await Promise.all(promises);
      setSelectedPosts([]);
      fetchBlogPosts();
      showSuccess(`Bulk ${action} completed`, `${selectedPosts.length} blog post${selectedPosts.length === 1 ? '' : 's'} ${action}ed successfully!`);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : `Failed to ${action} blog posts`;
      setError(errorMessage);
      showError(`Failed to ${action} blog posts`, errorMessage);
    } finally {
      setActionLoading(null);
    }
  };

  // Handle individual actions
  const handleAction = async (action: string, item: BlogPost) => {
    setActionLoading(`${action}-${item.id}`);
    try {
      switch (action) {
        case 'edit':
          showInfo('Opening Editor', `Editing "${item.title}"`);
          setEditingPost(item);
          setIsEditModalOpen(true);
          break;

        case 'view':
          showInfo('Opening View', `Viewing "${item.title}"`);
          // TODO: Implement view functionality
          showInfo('View Blog Post', `Opening "${item.title}" in new tab`);
          break;

        case 'delete':
          showInfo('Delete Confirmation', `Preparing to delete "${item.title}"`);
          showDeleteConfirmation(item);
          break;

        case 'toggle-published':
          const newStatus = !item.isPublished;
          showLoading(
            newStatus ? 'Publishing Blog Post' : 'Unpublishing Blog Post',
            `${newStatus ? 'Publishing' : 'Unpublishing'} "${item.title}"...`
          );
          await handleUpdate(item.id, { isPublished: newStatus });
          showSuccess(
            newStatus ? 'Blog Post Published' : 'Blog Post Unpublished',
            `"${item.title}" ${newStatus ? 'published' : 'unpublished'} successfully!`
          );
          break;

        case 'duplicate':
          await handleDuplicate(item);
          break;

        case 'archive':
          await handleArchive(item);
          break;

        default:
          console.warn(`Unknown action: ${action}`);
      }
    } finally {
      setActionLoading(null);
    }
  };

  // Handle sorting
  const handleSort = (field: string) => {
    const newOrder = sortBy === field ? (sortOrder === 'asc' ? 'desc' : 'asc') : 'asc';
    const newField = sortBy === field ? field : field;
    
    setSortBy(newField);
    setSortOrder(newOrder);
    setCurrentPage(1);
    
    showInfo('Sorting Blog Posts', `Sorting by ${field} (${newOrder})`);
  };

  // Handle selection
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedPosts(blogPosts.map(post => post.id));
      showInfo('Selection Updated', `Selected all ${blogPosts.length} blog post${blogPosts.length === 1 ? '' : 's'}`);
    } else {
      setSelectedPosts([]);
      showInfo('Selection Cleared', 'Deselected all blog posts');
    }
  };

  const handleSelectPost = (id: string, checked: boolean) => {
    if (checked) {
      setSelectedPosts([...selectedPosts, id]);
      const post = blogPosts.find(p => p.id === id);
      showInfo('Post Selected', `"${post?.title || 'Blog post'}" added to selection`);
    } else {
      setSelectedPosts(selectedPosts.filter(postId => postId !== id));
      const post = blogPosts.find(p => p.id === id);
      showInfo('Post Deselected', `"${post?.title || 'Blog post'}" removed from selection`);
    }
  };

  // Get visible fields for table
  const getVisibleFields = () => {
    if (visibleColumns.length > 0) {
      return config.fields?.filter(field => visibleColumns.includes(field.key)) || [];
    }
    return config.fields || [];
  };

  // Handle view mode change
  const handleViewModeChange = (mode: 'list' | 'grid') => {
    setViewMode(mode);
    showInfo('View Mode Changed', `Switched to ${mode} view`);
  };

  // Handle density change
  const handleDensityChange = (newDensity: 'compact' | 'comfortable' | 'spacious') => {
    setDensity(newDensity);
    showInfo('Density Updated', `Changed to ${newDensity} density`);
  };

  // Handle grid columns change
  const handleGridColumnsChange = (columns: number) => {
    setGridColumns(columns);
    showInfo('Grid Layout Updated', `Changed to ${columns} column${columns === 1 ? '' : 's'} layout`);
  };

  // Handle filter changes
  const handleFilterChange = (key: string, value: string) => {
    const newFilters = { ...filters };
    if (value) {
      newFilters[key] = value;
    } else {
      delete newFilters[key];
    }
    setFilters(newFilters);
    showInfo('Filter Applied', `Filter updated: ${key} = ${value || 'all'}`);
  };

  // Close all action menus
  const closeAllActionMenus = () => {
    setActiveActionMenu(null);
  };

  // Grid Card Component
  const GridCard = ({ post }: { post: BlogPost }) => {
    const isSelected = selectedPosts.includes(post.id);
    const classes = getGridDensityClasses();

    return (
      <div
        data-post-id={post.id}
        className={`group relative bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-all duration-200 ${
          isSelected ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
        } ${classes.card}`}
      >
        {/* Featured Image */}
        <div className="aspect-video bg-gray-100 rounded-t-lg overflow-hidden">
          {post.featuredImageUrl ? (
            <img
              src={post.featuredImageUrl}
              alt={post.title}
              className="w-full h-full object-cover"
            />
          ) : (
            <div className="w-full h-full flex items-center justify-center">
              <DocumentTextIcon className="w-12 h-12 text-gray-400" />
            </div>
          )}
        </div>

        {/* Content */}
        <div className={classes.content}>
          {/* Title */}
          <h3 className={`${classes.title} text-gray-900 line-clamp-2`}>
            {post.title}
          </h3>

          {/* Excerpt */}
          {post.excerpt && (
            <p className={`${classes.excerpt} text-gray-600 line-clamp-3`}>
              {post.excerpt}
            </p>
          )}

              {/* Meta Information */}
              <div className={classes.meta}>
                {/* Status */}
                <div className="flex items-center justify-between">
                  <span className={`inline-flex ${classes.status} font-semibold rounded-full ${
                    post.isPublished
                      ? 'bg-green-100 text-green-800'
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {post.isPublished ? 'Published' : 'Draft'}
                  </span>
                </div>

                {/* Categories */}
                {post.categories && (
                  <div className={`flex flex-wrap ${classes.categories}`}>
                    {post.categories.split(',').slice(0, 2).map((category: string, index: number) => (
                      <span key={index} className={`inline-flex ${classes.category} bg-blue-100 text-blue-800 rounded`}>
                        {category.trim()}
                      </span>
                    ))}
                  </div>
                )}
              </div>

              {/* Grid Card Footer - Exact same as clients management */}
              <div className={`flex items-center justify-between ${density === 'compact' ? 'mt-3 pt-2' : density === 'spacious' ? 'mt-5 pt-4' : 'mt-4 pt-3'} border-t border-gray-200`}>
                <div className="text-xs text-gray-500">
                  Updated {formatDate(post.updatedAt)}
                </div>

                {/* Mobile Action Button - Exact same as clients management */}
                <div className="lg:hidden">
                  <button
                    className="action-button p-2 text-gray-600 hover:text-gray-900 bg-gray-100 hover:bg-gray-200 rounded-md transition-colors"
                    onClick={(e) => {
                      e.stopPropagation()
                      const isVisible = activeActionMenu === post.id;
                      if (isVisible) {
                        // Hide menu
                        setActiveActionMenu(null);
                      } else {
                        // Show menu (this will automatically hide any other open menu)
                        setActiveActionMenu(post.id);
                      }
                    }}
                    title="Show Actions"
                  >
                    <EllipsisVerticalIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>

          {/* Actions Sidebar - Professional Overlay */}
          <div className={`action-menu absolute bg-white rounded-lg border border-gray-200 shadow-lg flex flex-col items-center justify-center transition-all duration-200 z-50 ${
            density === 'compact'
              ? 'top-1 right-1 bottom-1 w-12 space-y-1.5 px-1'
              : density === 'spacious'
              ? 'top-3 right-3 bottom-3 w-16 space-y-3 px-2'
              : 'top-2 right-2 bottom-2 w-14 space-y-2 px-1.5'
          }`} style={{
            opacity: activeActionMenu === post.id ? '1' : '0',
            transform: activeActionMenu === post.id ? 'translateX(0)' : 'translateX(100%)',
            pointerEvents: activeActionMenu === post.id ? 'auto' : 'none'
          }}>
            {/* Edit Button */}
            <button
              onClick={(e) => {
                e.stopPropagation()
                closeAllActionMenus()
                handleAction('edit', post)
              }}
              className={`group/btn relative inline-flex items-center justify-center bg-blue-600 hover:bg-blue-700 border border-blue-500 hover:border-blue-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto ${
                density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'
              }`}
              title="Edit Blog Post"
            >
              <PencilIcon className={`group-hover/btn:scale-110 ${density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5'}`} />
            </button>
            
            {/* View Button */}
            <button
              onClick={(e) => {
                e.stopPropagation()
                closeAllActionMenus()
                handleAction('view', post)
              }}
              className={`group/btn relative inline-flex items-center justify-center bg-indigo-600 hover:bg-indigo-700 border border-indigo-500 hover:border-indigo-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto ${
                density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'
              }`}
              title="View Blog Post"
            >
              <EyeIcon className={`group-hover/btn:scale-110 ${density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5'}`} />
            </button>

            {/* Toggle Published Button */}
            <button
              onClick={(e) => {
                e.stopPropagation()
                closeAllActionMenus()
                handleAction('toggle-published', post)
              }}
              className={`group/btn relative inline-flex items-center justify-center ${
                post.isPublished
                  ? 'bg-orange-600 hover:bg-orange-700 border-orange-500 hover:border-orange-600'
                  : 'bg-green-600 hover:bg-green-700 border-green-500 hover:border-green-600'
              } border text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto ${
                density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'
              }`}
              title={post.isPublished ? 'Unpublish Blog Post' : 'Publish Blog Post'}
            >
              {post.isPublished ? (
                <EyeSlashIcon className={`group-hover/btn:scale-110 ${density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5'}`} />
              ) : (
                <PowerIcon className={`group-hover/btn:scale-110 ${density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5'}`} />
              )}
            </button>

            {/* Duplicate Button */}
            <button
              onClick={(e) => {
                e.stopPropagation()
                closeAllActionMenus()
                handleAction('duplicate', post)
              }}
              className={`group/btn relative inline-flex items-center justify-center bg-purple-600 hover:bg-purple-700 border border-purple-500 hover:border-purple-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto ${
                density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'
              }`}
              title="Duplicate Blog Post"
            >
              <DocumentDuplicateIcon className={`group-hover/btn:scale-110 ${density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5'}`} />
            </button>

            {/* Archive Button */}
            <button
              onClick={(e) => {
                e.stopPropagation()
                closeAllActionMenus()
                handleAction('archive', post)
              }}
              className={`group/btn relative inline-flex items-center justify-center bg-yellow-600 hover:bg-yellow-700 border border-yellow-500 hover:border-yellow-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto ${
                density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'
              }`}
              title="Archive Blog Post"
            >
              <ArchiveBoxIcon className={`group-hover/btn:scale-110 ${density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5'}`} />
            </button>

            {/* Delete Button */}
            <button
              onClick={(e) => {
                e.stopPropagation()
                closeAllActionMenus()
                handleAction('delete', post)
              }}
              className={`group/btn relative inline-flex items-center justify-center bg-red-600 hover:bg-red-700 border border-red-500 hover:border-red-600 text-white rounded-lg shadow-md hover:shadow-lg focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 cursor-pointer hover:scale-110 pointer-events-auto ${
                density === 'compact' ? 'w-8 h-8' : density === 'spacious' ? 'w-12 h-12' : 'w-10 h-10'
              }`}
              title="Delete Blog Post"
            >
              <TrashIcon className={`group-hover/btn:scale-110 ${density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-6 w-6' : 'h-5 w-5'}`} />
            </button>
          </div>


          {/* Checkbox for bulk actions */}
          {config.enableBulkActions && (
            <div className={`absolute top-2 left-2 ${classes.checkbox}`}>
              <input
                type="checkbox"
                checked={isSelected}
                onChange={(e) => handleSelectPost(post.id, e.target.checked)}
                className={`rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white ${classes.checkbox}`}
                style={{ backgroundColor: 'white' }}
              />
            </div>
          )}
        </div>
      </div>
    );
  };

  // Grid density styling helper
  const getGridDensityClasses = () => {
    const baseClasses = {
      container: {
        compact: 'p-1',
        comfortable: 'p-3',
        spacious: 'p-4'
      },
      grid: {
        compact: 'gap-2',
        comfortable: 'gap-3',
        spacious: 'gap-4'
      },
      card: {
        compact: 'p-1',
        comfortable: 'p-2',
        spacious: 'p-3'
      },
      content: {
        compact: 'p-1',
        comfortable: 'p-2',
        spacious: 'p-3'
      },
      title: {
        compact: 'text-sm font-semibold mb-0.5',
        comfortable: 'text-lg font-semibold mb-1',
        spacious: 'text-xl font-semibold mb-2'
      },
      excerpt: {
        compact: 'text-xs mb-1',
        comfortable: 'text-sm mb-1.5',
        spacious: 'text-base mb-2'
      },
      meta: {
        compact: 'space-y-0.5 mb-1',
        comfortable: 'space-y-1 mb-2',
        spacious: 'space-y-2 mb-3'
      },
      status: {
        compact: 'px-1 py-0.5 text-xs',
        comfortable: 'px-1.5 py-0.5 text-xs',
        spacious: 'px-2 py-1 text-sm'
      },
      date: {
        compact: 'text-xs',
        comfortable: 'text-xs',
        spacious: 'text-sm'
      },
      categories: {
        compact: 'gap-0.5',
        comfortable: 'gap-0.5',
        spacious: 'gap-1'
      },
      category: {
        compact: 'px-1 py-0.5 text-xs',
        comfortable: 'px-1.5 py-0.5 text-xs',
        spacious: 'px-2 py-1 text-sm'
      },
      actions: {
        compact: 'pt-1',
        comfortable: 'pt-1.5',
        spacious: 'pt-2'
      },
      buttons: {
        compact: 'space-x-0.5',
        comfortable: 'space-x-1',
        spacious: 'space-x-2'
      },
      button: {
        compact: 'p-0.5',
        comfortable: 'p-1',
        spacious: 'p-1.5'
      },
      icon: {
        compact: 'w-2.5 h-2.5',
        comfortable: 'w-3 h-3',
        spacious: 'w-4 h-4'
      },
      checkbox: {
        compact: 'h-2.5 w-2.5',
        comfortable: 'h-3 w-3',
        spacious: 'h-4 w-4'
      }
    };

    return {
      container: baseClasses.container[density],
      grid: baseClasses.grid[density],
      card: baseClasses.card[density],
      content: baseClasses.content[density],
      title: baseClasses.title[density],
      excerpt: baseClasses.excerpt[density],
      meta: baseClasses.meta[density],
      status: baseClasses.status[density],
      date: baseClasses.date[density],
      categories: baseClasses.categories[density],
      category: baseClasses.category[density],
      actions: baseClasses.actions[density],
      buttons: baseClasses.buttons[density],
      button: baseClasses.button[density],
      icon: baseClasses.icon[density],
      checkbox: baseClasses.checkbox[density]
    };
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Truncate text
  const truncateText = (text: string, maxLength: number) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  return (
    <div className="h-full flex flex-col space-y-4">
      {/* CSS for hover effects and density */}
      <style jsx global>{`
        /* Action menu transitions only */
        .action-menu {
          transition: all 0.2s ease-in-out;
        }
        /* Desktop hover behavior */
        @media (min-width: 1025px) {
          .group:hover .action-menu {
            opacity: 1 !important;
            transform: translateX(0) !important;
            pointer-events: auto !important;
          }
        }
        .density-compact th {
          padding-top: 12px !important;
          padding-bottom: 12px !important;
        }
        .density-compact td {
          padding-top: 4px !important;
          padding-bottom: 4px !important;
        }
        .density-comfortable th {
          padding-top: 12px !important;
          padding-bottom: 12px !important;
        }
        .density-comfortable td {
          padding-top: 16px !important;
          padding-bottom: 16px !important;
        }
        .density-spacious th {
          padding-top: 12px !important;
          padding-bottom: 12px !important;
        }
        .density-spacious td {
          padding-top: 32px !important;
          padding-bottom: 32px !important;
        }
      `}</style>
      
      {/* Header */}
      <div className="relative rounded-lg shadow-sm border border-gray-200/50 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50/30 via-transparent to-indigo-50/20" />

        <div className="relative p-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <DocumentTextIcon className="h-14 w-14 text-lime-600 -mt-2" />
              <div>
                <h1 className="text-3xl font-bold text-gray-900 mt-2">
                  Blog Management
                </h1>
                <p className="text-sm font-medium text-gray-600">
                  Create, edit, and manage your blog content.
                </p>
              </div>
            </div>

            <div className="flex items-center">
              <button
                onClick={handleAddClick}
                className="inline-flex items-center px-4 py-2 bg-blue-600 text-white text-sm font-medium rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors shadow-sm"
              >
                <PlusIcon className="h-4 w-4 mr-2" />
                <span className="hidden sm:inline">Add Blog Post</span>
                <span className="sm:hidden">Add</span>
              </button>
            </div>
          </div>

        </div>
      </div>

      {/* Content Area */}
      <div
        className="flex-1 rounded-lg shadow-sm border border-gray-200/50 overflow-hidden"
        role="main"
        aria-label="Blog management section"
      >
        <div className="space-y-3" style={{ paddingBottom: '0' }}>
          {/* Search and View Controls - Responsive Design */}
          <div className="space-y-3 p-2 bg-white rounded-lg border border-gray-200 shadow-sm">
            {/* Mobile Layout - Stacked */}
            <div className="flex flex-col space-y-3 lg:hidden">
              {/* Search Bar - Full Width on Mobile */}
              <div className="w-full">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search blog posts..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 pr-4 py-2.5 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                  />
                </div>
              </div>

            {/* Mobile Controls - Single Row */}
            <div className="flex items-center gap-1">
              {/* View Mode Toggle - Stretched */}
              <div className="flex items-center bg-gray-100 rounded-lg p-0.5 flex-1">
                <button
                  onClick={() => handleViewModeChange('list')}
                  className={`flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 ${
                    viewMode === 'list'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="List view"
                >
                  <ListBulletIcon className="h-3 w-3" />
                  <span className="text-xs font-medium hidden xs:inline">List</span>
                </button>
                <button
                  onClick={() => handleViewModeChange('grid')}
                  className={`flex-1 px-2 py-1.5 rounded-md flex items-center justify-center gap-0.5 ${
                    viewMode === 'grid'
                      ? 'bg-white text-blue-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-900'
                  }`}
                  title="Grid view"
                >
                  <Squares2X2Icon className="h-3 w-3" />
                  <span className="text-xs font-medium hidden xs:inline">Grid</span>
                </button>
              </div>

              {/* Grid Columns Control (for grid view) */}
              {viewMode === 'grid' && (
                <div className="flex items-center bg-gray-100 rounded-lg p-0.5 flex-1">
                  <span className="text-xs font-medium text-gray-700 px-1">Col:</span>
                  <div className="flex items-center gap-0.5 flex-1">
                    {[1, 2, 3, 4].map((num) => (
                      <button
                        key={num}
                        onClick={() => handleGridColumnsChange(num)}
                        className={`flex-1 px-1.5 py-1 rounded text-xs font-medium ${
                          gridColumns === num
                            ? 'bg-white text-green-600 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                        title={`${num} column${num > 1 ? 's' : ''}`}
                      >
                        {num}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Table Columns Control (for list view) */}
              {viewMode === 'list' && (
                <div className="relative flex-1">
                  <button
                    onClick={() => setShowColumnSelector(!showColumnSelector)}
                    className="w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                    title="Columns"
                  >
                    <AdjustmentsHorizontalIcon className="h-3 w-3 mr-0.5" />
                    <span className="hidden xs:inline">Col</span>
                    <ChevronDownIcon className="h-3 w-3 ml-0.5" />
                  </button>
                </div>
              )}

              {/* Filters Button - Stretched */}
              <div className="relative flex-1">
                <button
                  onClick={() => setShowFilters(!showFilters)}
                  className={`w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium rounded-lg border ${
                    showFilters || Object.keys(filters).some(key => filters[key])
                      ? 'bg-blue-50 text-blue-700 border-blue-300'
                      : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                  }`}
                  title="Filters"
                >
                  <FunnelIcon className="h-3 w-3 mr-0.5" />
                  <span className="hidden xs:inline">Filter</span>
                  {Object.keys(filters).some(key => filters[key]) && (
                    <span className="ml-0.5 inline-flex items-center px-1 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {Object.values(filters).filter(Boolean).length}
                    </span>
                  )}
                </button>
              </div>

              {/* Density Control - Stretched */}
              <div className="relative flex-1">
                <button
                  onClick={() => setShowWindowList(!showWindowList)}
                  className="w-full inline-flex items-center justify-center px-2 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
                  title="Density"
                >
                  <AdjustmentsHorizontalIcon className="h-3 w-3 mr-0.5" />
                  <span className="hidden xs:inline">{density.charAt(0).toUpperCase() + density.slice(1)}</span>
                  <ChevronDownIcon className="h-3 w-3 ml-0.5" />
                </button>
              </div>

            </div>
              </div>

              {/* Desktop Layout - Horizontal */}
              <div className="hidden lg:flex items-center justify-between gap-4">
                {/* Search Bar and Filters */}
                <div className="flex items-center gap-3 flex-1 max-w-md">
                  <div className="relative flex-1">
                    <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search blog posts by title, content, excerpt..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="pl-10 pr-4 py-2 w-full border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm"
                    />
                  </div>
                  
                  {/* Filters Dropdown */}
                  <div className="relative dropdown-container">
                    <button
                      onClick={() => setShowFilters(!showFilters)}
                      className={`inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500 ${
                        showFilters || Object.keys(filters).some(key => filters[key])
                          ? 'bg-blue-50 text-blue-700 border-blue-300'
                          : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                      }`}
                      title="Show/hide filters"
                    >
                      <FunnelIcon className="h-4 w-4 mr-2" />
                      Filters
                      {Object.keys(filters).some(key => filters[key]) && (
                        <span className="ml-2 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {Object.values(filters).filter(Boolean).length}
                        </span>
                      )}
                      <ChevronDownIcon className="h-4 w-4 ml-2" />
                    </button>

                    {/* Filters Dropdown */}
                    {showFilters && (
                      <div className="absolute top-full right-0 mt-1 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                        <div className="p-4">
                          <div className="flex items-center justify-between mb-4">
                            <h3 className="text-sm font-medium text-gray-900">Filters</h3>
                            <button
                              onClick={() => setShowFilters(false)}
                              className="text-gray-400 hover:text-gray-600"
                            >
                              <XMarkIcon className="h-4 w-4" />
                            </button>
                          </div>
                          
                          <div className="space-y-4">
                            {config.filters?.map((filter) => (
                              <div key={filter.key}>
                                <label className="block text-xs font-medium text-gray-700 mb-2">
                                  {filter.label}
                                </label>
                                <select
                                  value={filters[filter.key] || ''}
                                  onChange={(e) => {
                                    const newFilters = { ...filters }
                                    if (e.target.value) {
                                      newFilters[filter.key] = e.target.value
                                    } else {
                                      delete newFilters[filter.key]
                                    }
                                    setFilters(newFilters)
                                  }}
                                  className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                >
                                  {filter.options?.map((option) => (
                                    <option key={option.value} value={option.value}>
                                      {option.label}
                                    </option>
                                  ))}
                                </select>
                              </div>
                            ))}
                          </div>
                          
                          <div className="flex justify-end mt-4 pt-4 border-t border-gray-200">
                            <button
                              onClick={() => {
                                setFilters({})
                                setShowFilters(false)
                              }}
                              className="px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg"
                            >
                              Clear All
                            </button>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Desktop Controls */}
                <div className="flex items-center space-x-3">
                  {/* View Mode Toggle */}
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-700">View:</span>
                    <div className="flex items-center bg-gray-100 rounded-lg p-1 gap-1">
                      <button
                        onClick={() => handleViewModeChange('list')}
                        className={`px-3 py-2 rounded-md flex items-center gap-2 ${
                          viewMode === 'list'
                            ? 'bg-white text-blue-600 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                        title="List view"
                      >
                        <ListBulletIcon className="h-5 w-5" />
                        <span className="text-sm font-medium">List</span>
                      </button>
                      <button
                        onClick={() => handleViewModeChange('grid')}
                        className={`px-3 py-2 rounded-md flex items-center gap-2 ${
                          viewMode === 'grid'
                            ? 'bg-white text-blue-600 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                        title="Grid view"
                      >
                        <Squares2X2Icon className="h-5 w-5" />
                        <span className="text-sm font-medium">Grid</span>
                      </button>
                    </div>
                  </div>

                  {/* Grid Columns Control (for grid view) */}
                  {viewMode === 'grid' && (
                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium text-gray-700">Columns:</span>
                      <div className="flex items-center bg-gray-100 rounded-lg p-1 gap-1">
                        <button
                          onClick={() => handleGridColumnsChange(1)}
                          className={`px-2 py-1 rounded text-xs font-medium ${
                            gridColumns === 1
                              ? 'bg-white text-green-600 shadow-sm'
                              : 'text-gray-600 hover:text-gray-900'
                          }`}
                          title="1 column"
                        >
                          1
                        </button>
                        <button
                          onClick={() => handleGridColumnsChange(2)}
                          className={`px-2 py-1 rounded text-xs font-medium ${
                            gridColumns === 2
                              ? 'bg-white text-green-600 shadow-sm'
                              : 'text-gray-600 hover:text-gray-900'
                          }`}
                          title="2 columns"
                        >
                          2
                        </button>
                        <button
                          onClick={() => handleGridColumnsChange(3)}
                          className={`px-2 py-1 rounded text-xs font-medium ${
                            gridColumns === 3
                              ? 'bg-white text-green-600 shadow-sm'
                              : 'text-gray-600 hover:text-gray-900'
                          }`}
                          title="3 columns"
                        >
                          3
                        </button>
                        <button
                          onClick={() => handleGridColumnsChange(4)}
                          className={`px-2 py-1 rounded text-xs font-medium ${
                            gridColumns === 4
                              ? 'bg-white text-green-600 shadow-sm'
                              : 'text-gray-600 hover:text-gray-900'
                          }`}
                          title="4 columns"
                        >
                          4
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Table Columns Control (for list view) */}
                  {viewMode === 'list' && (
                    <div className="relative dropdown-container">
                      <button
                        onClick={() => setShowColumnSelector(!showColumnSelector)}
                        className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                        title="Select columns to display"
                      >
                        <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
                        Columns
                        <ChevronDownIcon className="h-4 w-4 ml-2" />
                      </button>

                      {/* Column Selector Dropdown */}
                      {showColumnSelector && (
                        <div className="absolute top-full left-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                          <div className="p-2">
                            <div className="text-xs font-medium text-gray-500 uppercase tracking-wide mb-2">Show Columns</div>
                            {config.columns.map((column) => (
                              <label key={column.key} className="flex items-center space-x-2 py-1">
                                <input
                                  type="checkbox"
                                  checked={visibleColumns.includes(column.key as string)}
                                  onChange={(e) => {
                                    if (e.target.checked) {
                                      setVisibleColumns(prev => [...prev, column.key as string]);
                                    } else {
                                      setVisibleColumns(prev => prev.filter(col => col !== column.key));
                                    }
                                  }}
                                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <span className="text-sm text-gray-700">
                                  {column.label}
                                </span>
                              </label>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}

                  {/* Density */}
                  <div className="relative dropdown-container">
                    <button
                      onClick={() => setShowWindowList(!showWindowList)}
                      className="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
                      title="Select density"
                    >
                      <AdjustmentsHorizontalIcon className="h-4 w-4 mr-2" />
                      {density.charAt(0).toUpperCase() + density.slice(1)}
                      <ChevronDownIcon className="h-4 w-4 ml-2" />
                    </button>

                    {/* Density Dropdown */}
                    {showWindowList && (
                      <div className="absolute top-full left-0 mt-1 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-50">
                        <div className="p-1">
                          {(['compact', 'comfortable', 'spacious'] as const).map((option) => (
                            <button
                              key={option}
                              onClick={() => {
                                handleDensityChange(option)
                                setShowWindowList(false)
                              }}
                              className={`w-full text-left px-3 py-2 text-sm rounded-md hover:bg-gray-100 ${
                                density === option ? 'bg-blue-50 text-blue-700 font-medium' : 'text-gray-700'
                              }`}
                            >
                              {option.charAt(0).toUpperCase() + option.slice(1)}
                            </button>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                </div>
              </div>

              {/* Mobile Dropdowns */}
              {/* Filters Dropdown - Mobile */}
              {showFilters && (
                <div className="lg:hidden bg-white border border-gray-200 rounded-lg shadow-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-sm font-medium text-gray-900">Filters</h3>
                    <button
                      onClick={() => setShowFilters(false)}
                      className="text-gray-400 hover:text-gray-600"
                    >
                      <XMarkIcon className="h-4 w-4" />
                    </button>
                  </div>
                  
                  <div className="space-y-4">
                    {config.filters?.map((filter) => (
                      <div key={filter.key}>
                        <label className="block text-xs font-medium text-gray-700 mb-2">
                          {filter.label}
                        </label>
                        <select
                          value={filters[filter.key] || ''}
                          onChange={(e) => {
                            const newFilters = { ...filters }
                            if (e.target.value) {
                              newFilters[filter.key] = e.target.value
                            } else {
                              delete newFilters[filter.key]
                            }
                            setFilters(newFilters)
                          }}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        >
                          {filter.options?.map((option) => (
                            <option key={option.value} value={option.value}>
                              {option.label}
                            </option>
                          ))}
                        </select>
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex justify-end mt-4 pt-4 border-t border-gray-200">
                    <button
                      onClick={() => {
                        setFilters({})
                        setShowFilters(false)
                      }}
                      className="px-3 py-1.5 text-xs font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg"
                    >
                      Clear All
                    </button>
                  </div>
                </div>
              )}
            </div>

          {/* Bulk Actions Bar - List and Grid Views */}
          {config.enableBulkActions && selectedPosts.length > 0 && (viewMode === 'list' || viewMode === 'grid') && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg px-4 py-2 shadow-sm">
              <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-2">
                {/* Selection Info */}
                <div className="flex items-center space-x-2">
                  <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                    <span className="text-blue-600 font-semibold text-xs">
                      {selectedPosts.length}
                    </span>
                  </div>
                  <span className="text-xs font-medium text-blue-900">
                    blog post{selectedPosts.length === 1 ? '' : 's'} selected
                  </span>
                </div>

                {/* All Action Buttons - Single Row on Mobile and Desktop */}
                <div className="flex flex-wrap items-center gap-1.5">
                  <button
                    onClick={() => handleBulkAction('publish')}
                    className="inline-flex items-center px-2 py-1 text-xs font-medium text-green-700 bg-green-100 hover:bg-green-200 border border-green-300 rounded focus:outline-none focus:ring-1 focus:ring-green-500 transition-colors duration-200"
                    title="Publish selected blog posts"
                  >
                    <PowerIcon className="h-3 w-3 mr-1" />
                    <span className="hidden sm:inline">Publish</span>
                    <span className="sm:hidden">Pub</span>
                  </button>

                  <button
                    onClick={() => handleBulkAction('unpublish')}
                    className="inline-flex items-center px-2 py-1 text-xs font-medium text-orange-700 bg-orange-100 hover:bg-orange-200 border border-orange-300 rounded focus:outline-none focus:ring-1 focus:ring-orange-500 transition-colors duration-200"
                    title="Unpublish selected blog posts"
                  >
                    <EyeSlashIcon className="h-3 w-3 mr-1" />
                    <span className="hidden sm:inline">Unpublish</span>
                    <span className="sm:hidden">Unpub</span>
                  </button>

                  <button
                    onClick={() => handleBulkAction('duplicate')}
                    className="inline-flex items-center px-2 py-1 text-xs font-medium text-purple-700 bg-purple-100 hover:bg-purple-200 border border-purple-300 rounded focus:outline-none focus:ring-1 focus:ring-purple-500 transition-colors duration-200"
                    title="Duplicate selected blog posts"
                  >
                    <DocumentDuplicateIcon className="h-3 w-3 mr-1" />
                    <span className="hidden sm:inline">Duplicate</span>
                    <span className="sm:hidden">Dup</span>
                  </button>

                  <button
                    onClick={() => handleBulkAction('archive')}
                    className="inline-flex items-center px-2 py-1 text-xs font-medium text-yellow-700 bg-yellow-100 hover:bg-yellow-200 border border-yellow-300 rounded focus:outline-none focus:ring-1 focus:ring-yellow-500 transition-colors duration-200"
                    title="Archive selected blog posts"
                  >
                    <ArchiveBoxIcon className="h-3 w-3 mr-1" />
                    <span className="hidden sm:inline">Archive</span>
                    <span className="sm:hidden">Arch</span>
                  </button>

                  <button
                    onClick={() => showBulkDeleteConfirmation()}
                    className="inline-flex items-center px-2 py-1 text-xs font-medium text-red-700 bg-red-100 hover:bg-red-200 border border-red-300 rounded focus:outline-none focus:ring-1 focus:ring-red-500 transition-colors duration-200"
                    title="Delete selected blog posts"
                  >
                    <TrashIcon className="h-3 w-3 mr-1" />
                    <span className="hidden sm:inline">Delete</span>
                    <span className="sm:hidden">Del</span>
                  </button>

                  {/* Clear Selection Button - Same level as all other buttons */}
                  <button
                    onClick={() => setSelectedPosts([])}
                    className="inline-flex items-center px-2 py-1 text-xs font-medium text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded focus:outline-none focus:ring-1 focus:ring-gray-500 transition-colors duration-200"
                    title="Clear selection"
                  >
                    <XMarkIcon className="h-3 w-3 mr-1" />
                    <span className="hidden sm:inline">Clear</span>
                    <span className="sm:hidden">Clear</span>
                  </button>
                </div>
              </div>
            </div>
          )}

            {/* Error State */}
            {error && (
              <div className="px-6 py-4">
                <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <XMarkIcon className="h-5 w-5 text-red-400" />
                    </div>
                    <div className="ml-3">
                      <h3 className="text-sm font-medium text-red-800">Error</h3>
                      <div className="mt-2 text-sm text-red-700">
                        <p>{error}</p>
                      </div>
                      <div className="mt-4">
                        <button
                          onClick={() => {
                            setError(null);
                            fetchBlogPosts();
                          }}
                          className="bg-red-100 px-3 py-2 rounded-md text-sm font-medium text-red-800 hover:bg-red-200"
                        >
                          Try Again
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Loading State */}
            {loading && (
              <div className="px-6 py-8">
                <div className="flex items-center justify-center">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  <span className="ml-3 text-gray-600">Loading blog posts...</span>
                </div>
              </div>
            )}

            {/* Data Display */}
            {!loading && !error && (
              <div>
                {blogPosts.length === 0 ? (
                  /* Empty State */
                  <div className="p-12 text-center">
                    <DocumentTextIcon className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No blog posts found</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      {debouncedSearchQuery ? 'Try adjusting your search terms.' : 'Get started by creating your first blog post.'}
                    </p>
                    {config.permissions?.create && !debouncedSearchQuery && (
                      <div className="mt-6">
                        <button
                          onClick={handleAddClick}
                          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          Add Blog Post
                        </button>
                      </div>
                    )}
                  </div>
                ) : viewMode === 'list' ? (
                  /* Table View */
                  <div className="overflow-hidden">
                    <div className="overflow-x-auto">
                      <table className={`min-w-full divide-y divide-gray-200 density-${density}`}>
                        <thead className="bg-gray-200 border-b border-gray-300">
                          <tr>
                            {/* Checkbox Column */}
                            {config.enableBulkActions && (
                              <th scope="col" className="relative pl-2 py-2" style={{ width: '6px' }}>
                                <input
                                  type="checkbox"
                                  checked={selectedPosts.length === blogPosts.length && blogPosts.length > 0}
                                  onChange={(e) => handleSelectAll(e.target.checked)}
                                  className={`text-blue-600 focus:ring-blue-500 border-gray-300 rounded ${
                                    density === 'compact' ? 'h-4 w-4' : density === 'spacious' ? 'h-5 w-5' : 'h-4 w-4'
                                  }`}
                                />
                              </th>
                            )}

                            {/* Data Columns */}
                            {getVisibleFields().map((field) => (
                              <th
                                key={field.key}
                                scope="col"
                                className="px-6 py-2 text-left font-medium text-gray-900 uppercase tracking-wider cursor-pointer hover:bg-gray-100 text-xs"
                                onClick={() => handleSort(field.key)}
                              >
                                <div className="flex items-center space-x-1">
                                  <span>{field.label}</span>
                                  {sortBy === field.key ? (
                                    sortOrder === 'asc' ? (
                                      <ArrowUpIcon className="h-3 w-3 text-black" />
                                    ) : (
                                      <ArrowDownIcon className="h-3 w-3 text-black" />
                                    )
                                  ) : (
                                    <ArrowUpIcon className="h-3 w-3 text-gray-400" />
                                  )}
                                </div>
                              </th>
                            ))}

                            {/* Actions Column */}
                            {config.actions && config.actions.length > 0 && (
                              <th scope="col" className="px-6 py-2 text-right font-medium text-gray-900 uppercase tracking-wider text-xs min-w-[120px]">
                                <span>Actions</span>
                              </th>
                            )}
                          </tr>
                        </thead>
                        <tbody className="bg-white divide-y divide-gray-200">
                          {blogPosts.map((post) => (
                            <tr key={post.id} className={`hover:bg-gray-50 ${
                              selectedPosts.includes(post.id) ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                            }`}>
                              {/* Checkbox */}
                              {config.enableBulkActions && (
                                <td className="px-6">
                                  <input
                                    type="checkbox"
                                    checked={selectedPosts.includes(post.id)}
                                    onChange={(e) => handleSelectPost(post.id, e.target.checked)}
                                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white"
                                    style={{ backgroundColor: 'white' }}
                                  />
                                </td>
                              )}

                              {/* Data Cells */}
                              {getVisibleFields().map((field) => (
                                <td key={field.key} className="px-6 whitespace-nowrap">
                                  {field.key === 'title' ? (
                                    <div className="flex items-center">
                                      <BlogAvatar
                                        title={post.title}
                                        featuredImageUrl={post.featuredImageUrl}
                                        size="sm"
                                        className="mr-3"
                                      />
                                      <div>
                                        <div className="text-sm font-medium text-gray-900">{post.title}</div>
                                        <div className="text-sm text-gray-500">{post.slug}</div>
                                      </div>
                                    </div>
                                  ) : field.key === 'isPublished' ? (
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                      post.isPublished
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-gray-100 text-gray-800'
                                    }`}>
                                      {post.isPublished ? 'Published' : 'Draft'}
                                    </span>
                                  ) : field.key === 'excerpt' || field.key === 'content' ? (
                                    <div className="text-sm text-gray-900 max-w-xs truncate" title={post[field.key]}>
                                      {truncateText(post[field.key] || '', 50)}
                                    </div>
                                  ) : field.key === 'tags' || field.key === 'categories' ? (
                                    <div className="text-sm text-gray-900">
                                      {post[field.key] ? (
                                        <div className="flex flex-wrap gap-1">
                                          {(post[field.key] as string).split(',').slice(0, 2).map((tag: string, index: number) => (
                                            <span key={index} className="inline-flex px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
                                              {tag.trim()}
                                            </span>
                                          ))}
                                          {(post[field.key] as string).split(',').length > 2 && (
                                            <span className="text-xs text-gray-500">+{(post[field.key] as string).split(',').length - 2} more</span>
                                          )}
                                        </div>
                                      ) : '-'}
                                    </div>
                                  ) : field.key === 'createdAt' || field.key === 'updatedAt' || field.key === 'publishedAt' ? (
                                    <div className="text-sm text-gray-500">
                                      {post[field.key] ? formatDate(post[field.key] as string) : '-'}
                                    </div>
                                  ) : (
                                    <div className="text-sm text-gray-900">
                                      {post[field.key] || '-'}
                                    </div>
                                  )}
                                </td>
                              ))}

                              {/* Actions */}
                              {config.actions && config.actions.length > 0 && (
                                <td className="px-6 whitespace-nowrap text-right text-sm font-medium">
                                  <div className="flex items-center space-x-2">
                                    {config.actions.map((action) => {
                                      const isLoading = actionLoading === `${action.action}-${post.id}`;
                                      const IconComponent = action.icon === 'EyeIcon' ? EyeSlashIcon :
                                                           action.icon === 'PencilIcon' ? PencilIcon :
                                                           action.icon === 'PowerIcon' ? PowerIcon :
                                                           action.icon === 'TrashIcon' ? TrashIcon : DocumentTextIcon;

                                      return (
                                        <button
                                          key={action.action}
                                          onClick={() => handleAction(action.action, post)}
                                          disabled={isLoading}
                                          className={`p-1 rounded-md transition-colors ${
                                            action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' :
                                            action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' :
                                            action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' :
                                            action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' :
                                            'text-gray-600 hover:bg-gray-50'
                                          } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                                          title={action.tooltip}
                                        >
                                          {isLoading ? (
                                            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                                          ) : (
                                            <IconComponent className="w-4 h-4" />
                                          )}
                                        </button>
                                      );
                                    })}
                                  </div>
                                </td>
                              )}
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                ) : viewMode === 'grid' ? (
                  /* Grid View - Restructured */
                  <div className={getGridDensityClasses().container}>
                    <div className={`grid ${getGridDensityClasses().grid} ${
                      gridColumns === 1 ? 'grid-cols-1' :
                      gridColumns === 2 ? 'grid-cols-1 md:grid-cols-2' :
                      gridColumns === 3 ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3' :
                      'grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'
                    }`}>
                      {blogPosts.map((post) => (
                        <GridCard key={post.id} post={post} />
                      ))}
                    </div>
                  </div>
                ) : (
                  /* Card View */
                  <div className="p-6">
                    <div className="space-y-4">
                      {blogPosts.map((post) => (
                        <div key={post.id} className={`bg-white border border-gray-200 rounded-lg shadow-sm hover:shadow-md transition-shadow ${
                          selectedPosts.includes(post.id) ? 'bg-blue-50 border-l-4 border-l-blue-500' : ''
                        }`}>
                          <div className="flex">
                            {/* Featured Image */}
                            <div className="w-48 h-32 bg-gray-100 rounded-l-lg overflow-hidden flex-shrink-0">
                              {post.featuredImageUrl ? (
                                <img
                                  src={post.featuredImageUrl}
                                  alt={post.title}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center">
                                  <DocumentTextIcon className="w-8 h-8 text-gray-400" />
                                </div>
                              )}
                            </div>

                            {/* Content */}
                            <div className="flex-1 p-4">
                              <div className="flex items-start justify-between">
                                <div className="flex-1">
                                  {/* Title and Slug */}
                                  <h3 className="text-lg font-semibold text-gray-900 mb-1">
                                    {post.title}
                                  </h3>
                                  <p className="text-sm text-gray-500 mb-2">
                                    {post.slug}
                                  </p>

                                  {/* Excerpt */}
                                  {post.excerpt && (
                                    <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                                      {post.excerpt}
                                    </p>
                                  )}

                                  {/* Meta Information */}
                                  <div className="flex items-center space-x-4 text-sm text-gray-500">
                                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                      post.isPublished
                                        ? 'bg-green-100 text-green-800'
                                        : 'bg-gray-100 text-gray-800'
                                    }`}>
                                      {post.isPublished ? 'Published' : 'Draft'}
                                    </span>
                                    <span>Updated: {formatDate(post.updatedAt)}</span>
                                    {post.categories && (
                                      <span>Category: {post.categories.split(',')[0].trim()}</span>
                                    )}
                                  </div>
                                </div>

                                {/* Actions */}
                                <div className="flex items-center space-x-2 ml-4">
                                  {config.actions?.map((action) => {
                                    const isLoading = actionLoading === `${action.action}-${post.id}`;
                                    const IconComponent = action.icon === 'EyeIcon' ? EyeSlashIcon :
                                                         action.icon === 'PencilIcon' ? PencilIcon :
                                                         action.icon === 'PowerIcon' ? PowerIcon :
                                                         action.icon === 'TrashIcon' ? TrashIcon : DocumentTextIcon;

                                    return (
                                      <button
                                        key={action.action}
                                        onClick={() => handleAction(action.action, post)}
                                        disabled={isLoading}
                                        className={`p-2 rounded-md transition-colors ${
                                          action.variant === 'primary' ? 'text-blue-600 hover:bg-blue-50' :
                                          action.variant === 'secondary' ? 'text-gray-600 hover:bg-gray-50' :
                                          action.variant === 'warning' ? 'text-yellow-600 hover:bg-yellow-50' :
                                          action.variant === 'danger' ? 'text-red-600 hover:bg-red-50' :
                                          'text-gray-600 hover:bg-gray-50'
                                        } ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}`}
                                        title={action.tooltip}
                                      >
                                        {isLoading ? (
                                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current"></div>
                                        ) : (
                                          <IconComponent className="w-4 h-4" />
                                        )}
                                      </button>
                                    );
                                  })}

                                  {/* Checkbox for bulk actions */}
                                  {config.enableBulkActions && (
                                    <input
                                      type="checkbox"
                                      checked={selectedPosts.includes(post.id)}
                                      onChange={(e) => handleSelectPost(post.id, e.target.checked)}
                                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500 bg-white !bg-white"
                                      style={{ backgroundColor: 'white' }}
                                    />
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            )}
        </div>
      </div>

      {/* Modals */}
      <BlogModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreate}
        title="Create New Blog Post"
      />

      <BlogModal
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setEditingPost(null);
        }}
        onSubmit={async (formData) => {
          if (editingPost) {
            await handleUpdate(editingPost.id, formData);
          }
        }}
        title="Edit Blog Post"
        initialData={editingPost ?? undefined}
      />

      {/* Confirmation Modal */}
      <ConfirmationModal
        isOpen={deleteConfirmation.isOpen}
        title="Delete Confirmation"
        message={deleteConfirmation.isBulkDelete && deleteConfirmation.bulkPosts && deleteConfirmation.bulkPosts.length > 1 
          ? "Are you sure you want to delete these blog posts?" 
          : "Are you sure you want to delete this blog post?"}
        details={(() => {
          if (deleteConfirmation.isBulkDelete) {
            const count = deleteConfirmation.bulkPosts?.length || 0;
            if (count === 1 && deleteConfirmation.post) {
              return `This action will permanently delete "${deleteConfirmation.post.title}". This cannot be undone.`;
            }
            return `This action will permanently delete ${count} blog post${count === 1 ? '' : 's'}. This cannot be undone.`;
          }
          const post = deleteConfirmation.post;
          return `This action will permanently delete "${post?.title || 'this blog post'}". This cannot be undone.`;
        })()}
        confirmText="Delete"
        cancelText="Cancel"
        onConfirm={confirmDelete}
        onCancel={cancelDelete}
        type="danger"
        showVerification={true}
        verificationData={{
          canDelete: true,
          reason: deleteConfirmation.isBulkDelete 
            ? (deleteConfirmation.bulkPosts?.length === 1 && deleteConfirmation.post)
              ? `Blog post "${deleteConfirmation.post.title}" selected for deletion`
              : `${deleteConfirmation.bulkPosts?.length || 0} blog post${(deleteConfirmation.bulkPosts?.length || 0) === 1 ? '' : 's'} selected for deletion`
            : `Blog post "${deleteConfirmation.post?.title || 'Unknown'}" ready for deletion`
        }}
      />
    </div>
  );
}
